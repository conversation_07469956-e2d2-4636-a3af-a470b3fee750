"""
机械臂数字孪生系统 - 配置文件

此模块包含机械臂数字孪生系统的全局配置参数。
"""

import os

# 基础路径配置
BASE_PATH = 'D:\\Desktop\\机械臂数字孪生'

# 数据路径配置
MESH_FILE = os.path.join(BASE_PATH, '无重复节点_R2_a1a2a3.csv')
TRAIN_DATA_FOLDER = os.path.join(BASE_PATH, '多关节角度数据集\\data\\train')
TEST_DATA_FOLDER = os.path.join(BASE_PATH, '多关节角度数据集\\data\\test')
TRAIN_PARAMS_FILE = os.path.join(BASE_PATH, '多关节角度数据集\\data\\train_cases_parameters.csv')
TEST_PARAMS_FILE = os.path.join(BASE_PATH, '多关节角度数据集\\data\\test_cases_parameters.csv')
MODEL_FOLDER = os.path.join(BASE_PATH, '模型')
RESULT_FOLDER = os.path.join(BASE_PATH, '云图结果')

# 确保所有必要的文件夹存在
for folder in [TRAIN_DATA_FOLDER, TEST_DATA_FOLDER, MODEL_FOLDER, RESULT_FOLDER]:
    os.makedirs(folder, exist_ok=True)

# 角度配置 - 从CSV文件中读取
# 以下为示例角度，实际使用时会从TRAIN_PARAMS_FILE和TEST_PARAMS_FILE中读取
# 三维角度元组 (a1, a2, a3)
TRAIN_ANGLES_EXAMPLE = [
    (-90, -90, -90), (-80, -80, -80), (-70, -70, -70), (-60, -60, -60),
    (-50, -50, -50), (-40, -40, -40), (-30, -30, -30), (-20, -20, -20),
    (-10, -10, -10), (0, 0, 0), (10, 10, 10), (20, 20, 20), (30, 30, 30),
    (40, 40, 40), (50, 50, 50), (60, 60, 60), (70, 70, 70), (80, 80, 80),
    (90, 90, 90)
]
TEST_ANGLES_EXAMPLE = [
    (-85, -85, -85), (-75, -75, -75), (-65, -65, -65), (-55, -55, -55),
    (-45, -45, -45), (-35, -35, -35), (-25, -25, -25), (-15, -15, -15),
    (-5, -5, -5), (5, 5, 5), (15, 15, 15), (25, 25, 25), (35, 35, 35),
    (45, 45, 45), (55, 55, 55), (65, 65, 65), (75, 75, 75), (85, 85, 85)
]

# 模型配置
DEFAULT_MODEL_TYPE = 'rbf'
AVAILABLE_MODELS = ['rbf', 'tree', 'mlp', 'torch_rbf', 'pinn_mlp', 'pirbn', 'gnn', 'cnn', 'rnn', 'transformer', 'transformer_pinn']

# 模型超参数配置
MODEL_PARAMS = {
    'transformer_pinn': {
        'input_dim': 3,                 
        'd_model': 128,                
        'nhead': 8,                    
        'num_layers': 6,               
        'dropout_rate': 0.1,           
        'learning_rate': 0.001,        
        'batch_size': 32,              
        'epochs': 500,                 
        'physics_weight': 0.1           
    },
    'rbf': {
        'function': 'cubic'
    },
    'tree': {
        'max_depth': None
    },
    'mlp': {
        'input_dim': 3,                 # 输入维度（三个关节角度）
        'hidden_dims': [64, 128, 64],   # 隐藏层维度
        'dropout_rate': 0.1,            # Dropout比率
        'learning_rate': 0.0005,        # 学习率
        'batch_size': 64,               # 批次大小
        'epochs': 5000                   # 训练轮数
    },
    'cnn': {
        'input_channels': 1,
        'hidden_dims': [64, 128, 256],
        'kernel_size': 3,
        'stride': 1,
        'padding': 1,
        'dropout_rate': 0.2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 100,
        'pooling_size': 2
    },
    'torch_rbf': {
        'input_dim': 3,                 # 输入维度（三个关节角度）
        'n_centers': 20,                # RBF中心的数量
        'sigma': 1.0,                   # RBF核的宽度参数
        'learning_rate': 0.001,         # 学习率
        'batch_size': 64,               # 批次大小
        'epochs': 5000                   # 训练轮数
    },
    'pinn_mlp': {
        'input_dim': 3,                 # 输入维度（a1,a2,a3）
        'hidden_dims': [64, 128, 64], # 隐藏层维度
        'dropout_rate': 0.1,            # Dropout比率
        'learning_rate': 0.001,         # 学习率
        'batch_size': 64,               # 批次大小
        'epochs': 200,                  # 训练轮数
        'physics_weight': 0.05           # 物理约束权重
    },
    'pirbn': {
        'input_dim': 3,                 # 输入维度（a1,a2,a3）
        'n_centers': 1000,              # RBF中心点数量
        'learning_rate': 0.001,         # 学习率
        'batch_size': 64,               # 批次大小
        'epochs': 1000,                 # 训练轮数
        'physics_weight': 0.1           # 物理约束权重
    },
    'gnn': {
        'input_dim': 3,                 # 输入维度（三个关节角度）
        'hidden_dim': 64,               # 隐藏层维度
        'num_layers': 3,                # 图卷积层数
        'dropout_rate': 0.2,            # Dropout比率
        'learning_rate': 0.001,         # 学习率
        'batch_size': 32,               # 批次大小
        'epochs': 200                   # 训练轮数
    },
    'rnn': {
        'input_dim': 3,                 # 输入维度（三个关节角度）
        'hidden_dim': 128,              # 隐藏层维度
        'num_layers': 2,                # RNN层数
        'dropout_rate': 0.2,            # Dropout比率
        'rnn_type': 'LSTM',             # RNN类型（LSTM或GRU）
        'learning_rate': 0.001,         # 学习率
        'batch_size': 32,               # 批次大小
        'epochs': 200                   # 训练轮数
    },
    'transformer': {
        'input_dim': 3,                 # 输入维度（三个关节角度）
        'd_model': 128,                 # 模型维度
        'nhead': 8,                     # 注意力头数
        'num_layers': 6,                # Transformer层数
        'dropout_rate': 0.1,            # Dropout比率
        'learning_rate': 0.001,         # 学习率
        'batch_size': 32,               # 批次大小
        'epochs': 200                   # 训练轮数
    }
}

# 可视化配置
PLOT_CONFIG = {
    'figsize': (18, 6),
    'cmap_data': 'viridis',
    'cmap_error': 'coolwarm',
    'point_size': 10,
    'box_aspect': [3, 6, 1],
    'dpi': 100
}