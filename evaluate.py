"""
机械臂数字孪生系统 - 代理模型评估

此模块用于评估机械臂数字孪生系统的代理模型性能。
包括模型加载、性能评估和可视化功能。
"""

import os
import numpy as np
import pandas as pd
import joblib
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib import rcParams
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import argparse
import seaborn as sns
from datetime import datetime
from models import predict_with_models
from scipy.interpolate import griddata

# 设置字体和样式
rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体
rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
plt.style.use('seaborn-v0_8')  # 使用更现代的样式


def load_data(base_path, angles, data_folder='data', column_idx=3):
    """
    加载训练或测试数据

    参数:
        base_path (str): 数据基础路径
        angles (list): 角度列表，每个元素是一个包含三个角度的元组 (a1, a2, a3)
        data_folder (str): 数据文件夹名称
        column_idx (int): 目标列索引

    返回:
        tuple: (加载的数据，网格节点数据)
            - 加载的数据形状为 (节点数, 样本数)
            - 网格节点数据为DataFrame
    """
    # 获取网格节点数
    mesh_path = os.path.join(base_path, '无重复节点_R2_a1a2a3.csv')
    df = pd.read_csv(mesh_path, header=None)
    n_nodes = len(df)

    # 准备数据数组
    data_path = os.path.join(base_path, data_folder)
    data = np.zeros((n_nodes, len(angles)))

    # 加载每个角度组合的数据
    for idx, angle_tuple in enumerate(angles):
        a1, a2, a3 = angle_tuple  # 解包三个角度值
        file_name = f'data_a1_{int(a1)}_a2_{int(a2)}_a3_{int(a3)}.csv'
        file_path = os.path.join(data_path, file_name)
        try:
            data[:, idx] = pd.read_csv(file_path).iloc[:, column_idx].values
        except FileNotFoundError:
            print(f"警告: 文件 {file_path} 不存在，跳过")
            continue
        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {e}")
            continue

    return data, df


def evaluate_model(Y_test, Y_pred):
    """
    评估模型性能

    参数:
        y_test (numpy.ndarray): 真实值，形状为 (节点数, 样本数)
        y_pred (numpy.ndarray): 预测值，形状为 (节点数, 样本数)或(样本数,)或(1, 节点数, 样本数)

    返回:
        dict: 包含各种评估指标的字典
    """

    print(f"DEBUG: Y_test shape before evaluation: {Y_test.shape}")
    print(f"DEBUG: Y_pred shape before evaluation: {Y_pred.shape}")

    # 调整维度处理部分
    if len(Y_pred.shape) == 1:
        if Y_pred.shape[0] == Y_test.shape[1]:
            Y_pred = Y_pred.reshape(1, -1)
        else:
            print("WARNING: Y_pred 1D shape does not match n_samples")
            Y_pred = Y_pred.reshape(Y_test.shape)
    
    # Check and adjust prediction results dimension
    if len(Y_pred.shape) == 1:
        # This case is probably when Y_pred is for a single node across all samples
        # But if Y_test is (nodes, samples), then Y_pred should be (nodes, samples)
        # This branch might be problematic if Y_pred is flattened accidentally.
        # It needs to be reshaped to (n_nodes, n_samples)
        if Y_pred.shape[0] == Y_test.shape[1]: # if it's (n_samples,) meaning one node's prediction
             Y_pred = Y_pred.reshape(1, -1) # Reshape to (1, n_samples)
        else:
             print("WARNING: Y_pred 1D shape does not match n_samples, attempting reshape to Y_test.shape")
             Y_pred = Y_pred.reshape(Y_test.shape) # Fallback, might not be correct if logic is complex
             
    elif len(Y_pred.shape) == 3:
        # Handle CNN models 3D output (e.g., (1, nodes, samples))
        # Or potentially (samples, nodes, features) -> then need to extract features
        if Y_pred.shape[0] == 1 and Y_pred.shape[1] == Y_test.shape[0] and Y_pred.shape[2] == Y_test.shape[1]:
            Y_pred = Y_pred.squeeze(0) # Remove the batch dimension of 1
        elif Y_pred.shape[0] == Y_test.shape[1] and Y_pred.shape[1] == Y_test.shape[0] and Y_pred.shape[2] == 1:
            # Case if CNN output is (n_samples, n_nodes, 1)
            Y_pred = Y_pred.squeeze(-1).T # Squeeze last dim, then transpose to (n_nodes, n_samples)
        else:
            print(f"WARNING: Unexpected 3D Y_pred shape: {Y_pred.shape}. Attempting reshape to Y_test.shape.")
            Y_pred = Y_pred.reshape(Y_test.shape)
    elif Y_pred.shape[0] != Y_test.shape[0]:
        # This usually means Y_pred is (n_samples, n_nodes) and needs transpose to (n_nodes, n_samples)
        print("WARNING: Y_pred.shape[0] != Y_test.shape[0], attempting transpose.")
        Y_pred = Y_pred.T
    
    # Final check of shapes before evaluation loop
    if Y_test.shape != Y_pred.shape:
        raise ValueError(f"After processing, Y_test shape {Y_test.shape} and Y_pred shape {Y_pred.shape} still mismatch.")

    print(f"DEBUG: Y_test shape after processing: {Y_test.shape}")
    print(f"DEBUG: Y_pred shape after processing: {Y_pred.shape}")

    n_nodes = Y_test.shape[0]
    rmse_scores = []
    r2_scores = []
    mae_scores = []

    for i in range(n_nodes):
        # Now, y_true[i, :] and y_pred[i, :] should both be 1D arrays of length n_samples (2057)
        try:
            rmse = np.sqrt(mean_squared_error(Y_test[i, :], Y_pred[i, :]))
            r2 = r2_score(Y_test[i, :], Y_pred[i, :])
            mae = mean_absolute_error(Y_test[i, :], Y_pred[i, :])
        except ValueError as e:
            print(f"Error at node {i}: {e}")
            print(f"Y_test[i, :] shape: {Y_test[i, :].shape}")
            print(f"Y_pred[i, :] shape: {Y_pred[i, :].shape}")
            raise # Re-raise the error to stop execution and investigate

        rmse_scores.append(rmse)
        r2_scores.append(r2)
        mae_scores.append(mae)

    metrics = {
        'avg_rmse': np.mean(rmse_scores),
        'avg_r2': np.mean(r2_scores),
        'avg_mae': np.mean(mae_scores),
        'max_rmse': np.max(rmse_scores),
        'min_r2': np.min(r2_scores),
        'max_mae': np.max(mae_scores),
        'rmse_scores': rmse_scores,
        'r2_scores': r2_scores,
        'mae_scores': mae_scores
    }

    metrics.update({
        'true_values': Y_test,
        'pred_values': Y_pred,
        
    })
    
    return metrics


def calculate_strain_statistics(y_data, test_angles):
    """
    计算不同工况下的应变统计信息

    参数:
        y_data (numpy.ndarray): 应变数据，形状为 (节点数, 样本数)
        test_angles (list): 测试角度列表

    返回:
        pandas.DataFrame: 包含每个工况的平均应变和最大应变
    """
    strain_stats = []

    for i, angle_tuple in enumerate(test_angles):
        a1, a2, a3 = angle_tuple
        strain_values = y_data[:, i]

        stats = {
            'Case': f'a1={a1}_a2={a2}_a3={a3}',
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'Mean_Strain': np.mean(strain_values),
            'Max_Strain': np.max(strain_values),
            'Min_Strain': np.min(strain_values),
            'Std_Strain': np.std(strain_values),
            'Range_Strain': np.max(strain_values) - np.min(strain_values)
        }
        strain_stats.append(stats)

    return pd.DataFrame(strain_stats)


def plot_strain_comparison(true_stats, pred_stats, model_type, save_folder):
    """
    绘制不同工况下的应变对比图
    """
    # 只取前30个测试工况
    true_stats = true_stats.iloc[:30]
    pred_stats = pred_stats.iloc[:30]
    
    # 创建单个子图布局
    fig, ax = plt.subplots(1, 1, figsize=(32, 6))
    
    # 准备数据
    x_pos = np.arange(len(true_stats))
    case_labels = [f'Case {i+1}' for i in range(len(true_stats))]

    # 绘制仿真数据
    ax.plot(x_pos, true_stats['Mean_Strain'], 'o-', color='blue',
            markersize=6, linewidth=2, label='Simulation Mean')
    ax.plot(x_pos, true_stats['Max_Strain'], '^--', color='navy',
            markersize=6, linewidth=2, label='Simulation Max')
    
    # 绘制预测数据
    ax.plot(x_pos, pred_stats['Mean_Strain'], 's-', color='red',
            markersize=6, linewidth=2, label='Prediction Mean')
    ax.plot(x_pos, pred_stats['Max_Strain'], 'v--', color='darkred',
            markersize=6, linewidth=2, label='Prediction Max')

    ax.set_title(f'{model_type.upper()} Model: Strain Value Comparison', 
                fontsize=14, fontweight='bold')
    ax.legend(fontsize=12, loc='upper left', bbox_to_anchor=(1, 1))
    ax.grid(True, alpha=0.3)
    ax.tick_params(axis='both', labelsize=10)

    # 设置x轴标签
    case_labels = [f'Case {i+1}' for i in range(len(true_stats))]
    ax.set_xticks(x_pos)
    ax.set_xticklabels(case_labels, rotation=45, ha='right')

    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(save_folder, f'{model_type}_strain_comparison_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    return save_path


def plot_advanced_metrics(metrics, model_type, save_folder, true_stats, pred_stats):
    """
    绘制高级评估指标图表

    参数:
        metrics (dict): 评估指标字典
        model_type (str): 模型类型
        save_folder (str): 保存文件夹

    返回:
        str: 保存路径
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # RMSE分布直方图
    axes[0, 0].hist(metrics['rmse_scores'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(metrics['avg_rmse'], color='red', linestyle='--', linewidth=2, label=f'Mean: {metrics["avg_rmse"]:.4f}')
    axes[0, 0].set_title('RMSE Distribution')
    axes[0, 0].set_xlabel('RMSE')
    axes[0, 0].set_ylabel('Number of Nodes')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # R²分布直方图
    axes[0, 1].hist(metrics['r2_scores'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 1].axvline(metrics['avg_r2'], color='red', linestyle='--', linewidth=2, label=f'Mean: {metrics["avg_r2"]:.4f}')
    axes[0, 1].set_title('R² Distribution')
    axes[0, 1].set_xlabel('R²')
    axes[0, 1].set_ylabel('Number of Nodes')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # MAE分布直方图
    axes[0, 2].hist(metrics['mae_scores'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 2].axvline(metrics['avg_mae'], color='red', linestyle='--', linewidth=2, label=f'Mean: {metrics["avg_mae"]:.4f}')
    axes[0, 2].set_title('MAE Distribution')
    axes[0, 2].set_xlabel('MAE')
    axes[0, 2].set_ylabel('Number of Nodes')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)

    # # 密度散点图
    # 评估预测偏差：点云偏离对角线的程度反映系统误差
    # 识别预测趋势：点云分布形状显示预测值是否整体偏大/偏小
    # 检测异常预测：远离点云主体的离散点可能表示模型失效节点
    # 分析数据分布：点云形状反映应变值的集中区间和分布范围
    # # 提取前30个工况的统计量
    true_values = np.concatenate([
        true_stats.iloc[:30]['Mean_Strain'], 
        true_stats.iloc[:30]['Max_Strain']
    ])
    pred_values = np.concatenate([
        pred_stats.iloc[:30]['Mean_Strain'],
        pred_stats.iloc[:30]['Max_Strain']
    ])

    # 修改后的密度散点图
    kde_ax = axes[1, 0]
    sns.kdeplot(
        x=true_values,
        y=pred_values,
        ax=kde_ax,
        cmap='plasma',
        fill=True,
        levels=25,
        thresh=0.1,
        linewidths=0.5
    )
    kde_ax.plot([true_values.min(), true_values.max()], 
                [true_values.min(), true_values.max()], '--', color='gray', linewidth=0.5)
    kde_ax.set_title('Strain Density (Top 30 Cases)\nMean/Max Values')
    kde_ax.set_xlabel('True Strain')
    kde_ax.set_ylabel('Predicted Strain')

    # # 误差分布热力图
    # # 识别误差分布模式：垂直条纹表示某些节点在两种误差指标中均表现不佳
    # # 比较误差类型相关性：若颜色分布相似，说明RMSE和MAE具有一致性
    # # 发现异常节点：寻找颜色特别深的离散点
    # sns.heatmap(
    #     np.array([metrics['rmse_scores'], metrics['mae_scores']]).T,
    #     ax=axes[1, 0],
    #     cmap='viridis',
    #     yticklabels=False,
    #     cbar_kws={'label': 'Error Value'}
    # )
    # axes[1, 0].set_title('Error Distribution Heatmap')
    # axes[1, 0].set_xlabel('Error Type')
    # axes[1, 0].set_xticks([0.5, 1.5])
    # axes[1, 0].set_ylabel('Node Index') 
    # axes[1, 0].set_xticklabels(['RMSE', 'MAE'])

    # 指标散点图 (RMSE vs R²)
    axes[1, 1].scatter(metrics['rmse_scores'], metrics['r2_scores'], alpha=0.6, s=20)
    axes[1, 1].set_title('RMSE vs R² Scatter Plot')
    axes[1, 1].set_xlabel('RMSE')
    axes[1, 1].set_ylabel('R²')
    axes[1, 1].grid(True, alpha=0.3)

    # 累积分布函数
    sorted_rmse = np.sort(metrics['rmse_scores'])
    y_vals = np.arange(1, len(sorted_rmse) + 1) / len(sorted_rmse)
    axes[1, 2].plot(sorted_rmse, y_vals, linewidth=2)
    axes[1, 2].set_title('RMSE Cumulative Distribution')
    axes[1, 2].set_xlabel('RMSE')
    axes[1, 2].set_ylabel('Cumulative Probability')
    axes[1, 2].grid(True, alpha=0.3)

    plt.suptitle(f'{model_type.upper()} Model: Advanced Evaluation Metrics', fontsize=16)
    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(save_folder, f'{model_type}_advanced_metrics_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    return save_path


def plot_cloud(df, y_true, y_pred, error, title, save_path):
    """
    绘制云图，显示真实值、预测值和误差

    参数:
        df (pandas.DataFrame): 网格节点坐标数据
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        error (numpy.ndarray): 误差值
        title (str): 图表标题
        save_path (str): 保存路径
    """
    fig = plt.figure(figsize=(18, 6))
    ax1 = fig.add_subplot(131, projection='3d')
    ax2 = fig.add_subplot(132, projection='3d')
    ax3 = fig.add_subplot(133, projection='3d')

    # 真实值云图
    scatter1 = ax1.scatter(df.iloc[:, 0], df.iloc[:, 1], df.iloc[:, 2], c=y_true, cmap='viridis', s=10)
    ax1.set_title('Simulation')
    ax1.set_box_aspect([3, 6, 1])

    # 预测值云图
    scatter2 = ax2.scatter(df.iloc[:, 0], df.iloc[:, 1], df.iloc[:, 2], c=y_pred, cmap='viridis', s=10)
    ax2.set_title('Prediction')
    ax2.set_box_aspect([3, 6, 1])

    # 误差值云图
    scatter3 = ax3.scatter(df.iloc[:, 0], df.iloc[:, 1], df.iloc[:, 2], c=error, cmap='coolwarm', s=10)
    ax3.set_title('Error')
    ax3.set_box_aspect([3, 6, 1])

    # 添加颜色条
    cbar1 = fig.colorbar(scatter1, ax=ax1, shrink=0.9)
    cbar1.set_label('First Principal Strain')
    cbar1.set_ticks([np.min(y_true), np.max(y_true)])
    cbar1.set_ticklabels([f'{np.min(y_true):.4f}', f'{np.max(y_true):.4f}'])

    cbar2 = fig.colorbar(scatter2, ax=ax2, shrink=0.9)
    cbar2.set_ticks([np.min(y_pred), np.max(y_pred)])
    cbar2.set_label('First Principal Strain')
    cbar2.set_ticklabels([f'{np.min(y_pred):.4f}', f'{np.max(y_pred):.4f}'])

    cbar3 = fig.colorbar(scatter3, ax=ax3, shrink=0.9)
    cbar3.set_label('Error Value')
    cbar3.set_ticks([np.min(error), np.max(error)])
    cbar3.set_ticklabels([f'{np.min(error):.6f}', f'{np.max(error):.6f}'])

    plt.suptitle(title)
    plt.savefig(save_path, bbox_inches='tight')
    plt.close()


def plot_metrics_distribution(df, metrics, model_type, save_folder):
    """
    绘制指标分布云图
    
    参数:
        df (pd.DataFrame): 网格节点坐标数据
        metrics (dict): 包含rmse_scores, r2_scores, mae_scores的字典
        model_type (str): 模型类型
        save_folder (str): 保存路径
    """
    fig = plt.figure(figsize=(18, 6))
    
    # 创建三个子图
    ax1 = fig.add_subplot(131, projection='3d')
    ax2 = fig.add_subplot(132, projection='3d')
    ax3 = fig.add_subplot(133, projection='3d')

    # RMSE分布云图
    sc1 = ax1.scatter(df.iloc[:,0], df.iloc[:,1], df.iloc[:,2], 
                    c=metrics['rmse_scores'], cmap='viridis', s=10)
    ax1.set_title(f'RMSE Distribution\n(Mean: {metrics["avg_rmse"]:.4f})')
    ax1.set_box_aspect([3, 6, 1])
    fig.colorbar(sc1, ax=ax1, label='RMSE')

    # R²分布云图
    sc2 = ax2.scatter(df.iloc[:,0], df.iloc[:,1], df.iloc[:,2],
                    c=metrics['r2_scores'], cmap='plasma', s=10)
    ax2.set_title(f'R² Distribution\n(Mean: {metrics["avg_r2"]:.4f})')
    ax2.set_box_aspect([3, 6, 1])
    fig.colorbar(sc2, ax=ax2, label='R² Score')

    # MAE分布云图
    sc3 = ax3.scatter(df.iloc[:,0], df.iloc[:,1], df.iloc[:,2],
                    c=metrics['mae_scores'], cmap='inferno', s=10)
    ax3.set_title(f'MAE Distribution\n(Mean: {metrics["avg_mae"]:.4f})')
    ax3.set_box_aspect([3, 6, 1])
    fig.colorbar(sc3, ax=ax3, label='MAE')

    plt.suptitle(f'{model_type.upper()} Model Metrics Distribution', y=1.02)
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(save_folder, f'{model_type}_metrics_distribution_{timestamp}.png')
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    return save_path


def save_evaluation_results(metrics, model_type, total_time, test_angles, save_folder):
    # 创建结果文件路径（添加时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = os.path.join(save_folder, f'{model_type}_评估结果_{timestamp}.txt')

    # 计算平均预测时间
    avg_time = total_time / len(test_angles)

    # 写入结果
    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"\n{'-'*50}\n")
        f.write(f"{model_type} 模型评估结果\n")
        f.write(f"{'-'*50}\n\n")

        # 写入预测时间
        f.write(f"\n\n== 预测时间 ==\n")
        f.write(f"\u603b预测时间: {total_time:.3f} 秒\n")
        f.write(f"平均每个角度预测时间: {avg_time:.3f} 秒\n")
        f.write(f"测试角度数量: {len(test_angles)}\n")

        # 写入总体评估指标
        f.write(f"\n\n== 总体评估指标 ==\n")
        f.write(f"平均 RMSE: {metrics['avg_rmse']:.6f}\n")
        f.write(f"平均 R²: {metrics['avg_r2']:.6f}\n")
        f.write(f"平均 MAE: {metrics['avg_mae']:.6f}\n")
        f.write(f"最大 RMSE: {metrics['max_rmse']:.6f}\n")
        f.write(f"最小 R²: {metrics['min_r2']:.6f}\n")
        f.write(f"最大 MAE: {metrics['max_mae']:.6f}\n")

        # 写入测试角度
        f.write(f"\n\n== 测试角度 ==\n")
        f.write("角度组合 (a1, a2, a3):\n")
        for i, angle_tuple in enumerate(test_angles):
            a1, a2, a3 = angle_tuple
            f.write(f"{i+1}. ({a1}, {a2}, {a3})\n")

    return result_file


def main(args):
    """主函数"""
    # 设置路径
    base_path = args.base_path
    model_path = os.path.join(base_path, '模型', f'训练好的代理模型_a1a2a3_{args.model_type}.pkl')

    # 从CSV文件读取角度参数
    test_params_file = os.path.join(base_path, '多关节角度数据集', 'data', 'test_cases_parameters.csv')
    print(f"正在从 {test_params_file} 读取测试角度参数...")

    try:
        # 使用GBK编码读取CSV文件
        test_params_df = pd.read_csv(test_params_file, encoding='gbk')
        # 提取角度参数
        test_angles = [(row['a1'], row['a2'], row['a3']) for _, row in test_params_df.iterrows()]
        print(f"成功读取 {len(test_angles)} 组测试角度参数")
    except Exception as e:
        print(f"读取测试角度参数文件时出错: {e}")
        print("使用默认角度参数...")
        # 默认角度参数，仅在读取失败时使用
        test_angles = [
            (-85, -85, -85), (-75, -75, -75), (-65, -65, -65), (-55, -55, -55),
            (-45, -45, -45), (-35, -35, -35), (-25, -25, -25), (-15, -15, -15),
            (-5, -5, -5), (5, 5, 5), (15, 15, 15), (25, 25, 25), (35, 35, 35),
            (45, 45, 45), (55, 55, 55), (65, 65, 65), (75, 75, 75), (85, 85, 85)
        ]

    # 加载模型，处理NumPy版本兼容性问题
    print(f"正在加载模型: {model_path}")
    try:
        model_list = joblib.load(model_path)
    except (ModuleNotFoundError, ImportError) as e:
        if 'numpy._core' in str(e) or 'numpy.core' in str(e):
            print(f"错误: 模型文件存在NumPy版本兼容性问题")
            print("解决方案:")
            print("1. 重新训练该模型")
            print("2. 或者升级/降级NumPy版本以匹配模型训练时的版本")
            return
        else:
            raise e

    # 加载测试数据
    print("正在加载测试数据...")
    Y_test, df = load_data(
        base_path=base_path,
        angles=test_angles,
        data_folder='多关节角度数据集/data/test',  # 更新为新的数据文件夹路径
        column_idx=3
    )
    print(f"测试数据加载完成，形状: {Y_test.shape}")

    # 进行预测
    print("正在进行预测...")
    from tqdm import tqdm

    # 添加预测进度条
    print("开始模型预测...")
    Y_pred, total_time = predict_with_models(model_list, test_angles)
    avg_time = total_time / len(test_angles)
    print(f"预测完成，总预测时间: {total_time:.3f}秒，平均每个角度: {avg_time:.3f}秒")

    # 评估模型
    print("正在评估模型性能...")
    metrics = evaluate_model(Y_test, Y_pred)
    print(f"平均 RMSE: {metrics['avg_rmse']:.4f}")
    print(f"平均 R²: {metrics['avg_r2']:.4f}")
    print(f"平均 MAE: {metrics['avg_mae']:.4f}")

    # 创建保存文件夹
    save_folder = os.path.join(base_path, '云图结果', args.model_type)
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    # 计算应变统计信息
    print("正在计算应变统计信息...")
    true_strain_stats = calculate_strain_statistics(Y_test, test_angles)
    pred_strain_stats = calculate_strain_statistics(Y_pred, test_angles)

    # 保存应变统计信息到CSV
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    true_stats_path = os.path.join(save_folder, f'{args.model_type}_true_strain_stats_{timestamp}.csv')
    pred_stats_path = os.path.join(save_folder, f'{args.model_type}_pred_strain_stats_{timestamp}.csv')

    true_strain_stats.to_csv(true_stats_path, index=False)
    pred_strain_stats.to_csv(pred_stats_path, index=False)

    print(f"真实应变统计已保存到: {true_stats_path}")
    print(f"预测应变统计已保存到: {pred_stats_path}")

    # 绘制应变对比图
    print("正在生成应变对比图...")
    strain_plot_path = plot_strain_comparison(true_strain_stats, pred_strain_stats, args.model_type, save_folder)
    print(f"应变对比图已保存到: {strain_plot_path}")

    # 绘制高级评估指标图
    print("正在生成高级评估指标图...")
    # 修改函数调用，添加统计结果参数
    advanced_metrics_path = plot_advanced_metrics(
        metrics, 
        args.model_type, 
        save_folder,
        true_strain_stats.iloc[:30],  # 只取前30个工况
        pred_strain_stats.iloc[:30]
    )
    
    print(f"高级评估指标图已保存到: {advanced_metrics_path}")

    # 在生成高级评估指标图之后添加
    print("正在生成指标分布云图...")
    metrics_dist_path = plot_metrics_distribution(df, metrics, args.model_type, save_folder)
    print(f"指标分布云图已保存到: {metrics_dist_path}")
    
    # 将评估结果写入TXT文件
    result_file = save_evaluation_results(metrics, args.model_type, total_time, test_angles, save_folder)
    print(f"评估结果已保存到: {result_file}")

    # 注意：已移除传统评估指标分布图生成，使用高级评估指标图代替

    # 绘制每个角度的云图
    if args.plot_clouds:
        print("正在生成云图...")
        # 添加云图生成进度条
        for i, angle in tqdm(enumerate(test_angles), total=len(test_angles), desc="生成云图"):
            y_true = Y_test[:, i]
            y_pred = Y_pred[:, i]
            error = y_true - y_pred
            a1, a2, a3 = angle  # 解包三个角度值
            save_path = os.path.join(save_folder, f'{args.model_type}_角度_a1_{int(a1)}_a2_{int(a2)}_a3_{int(a3)}_云图.png')

            plot_cloud(
                df=df,
                y_true=y_true,
                y_pred=y_pred,
                error=error,
                title=f'{args.model_type}模型 - 角度组合(a1={a1}, a2={a2}, a3={a3})对比',
                save_path=save_path
            )
        print(f"所有云图已保存到: {save_folder}")


if __name__ == "__main__":
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='评估机械臂数字孪生系统的代理模型')

    # 基础参数
    parser.add_argument('--base_path', type=str, default='D:/Desktop/机械臂数字孪生',
                        help='项目基础路径')
    parser.add_argument('--model_type', type=str, default='rbf',
                        choices=['rbf', 'tree', 'mlp', 'torch_rbf', 'pinn_mlp', 'pirbn', 'gnn', 'cnn', 'rnn', 'transformer', 'transformer_pinn'],
                        help='模型类型')
    parser.add_argument('--plot_clouds', action='store_true',
                        help='是否生成云图')

    args = parser.parse_args()
    main(args)