#最终测试集样本数: 2057
#最终训练集样本数: 4802
import os
import re
import pandas as pd
from pyDOE import lhs
import numpy as np
import shutil
import ast # 用于安全地评估字符串表示的元组

# --- 配置 ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
FEA_DIR = os.path.join(BASE_DIR, 'fea') # fea 文件夹的路径

# 原始工况数据文件现在在 fea/rowdata 子文件夹中
ROWDATA_DIR = os.path.join(FEA_DIR, 'rowdata')
CASES_FILE = os.path.join(FEA_DIR, 'cases.csv') # cases.csv 依然在 fea 文件夹下

TRAIN_DIR = os.path.join(FEA_DIR, 'train')
TEST_DIR = os.path.join(FEA_DIR, 'test')
TEST_RATIO = 0.3  # 大约 30% 用于测试集

# --- 主函数 ---
def main():
    print("开始基于 'cases.csv' 文件进行数据集划分...")

    # 1. 确保 train 和 test 目录存在
    os.makedirs(TRAIN_DIR, exist_ok=True)
    os.makedirs(TEST_DIR, exist_ok=True)
    print(f"已确保 '{TRAIN_DIR}' 和 '{TEST_DIR}' 目录存在。")

    # 2. 从 cases.csv 读取工况信息
    if not os.path.exists(CASES_FILE):
        print(f"错误：未找到 'cases.csv' 文件，请确保其路径正确：{CASES_FILE}")
        return

    try:
        cases_df = pd.read_csv(CASES_FILE)
        # 将 '角度值' 列的字符串表示转换为元组
        cases_df['角度值'] = cases_df['角度值'].apply(ast.literal_eval)
        print(f"已从 '{CASES_FILE}' 读取 {len(cases_df)} 条工况数据。")
    except Exception as e:
        print(f"读取或解析 'cases.csv' 文件时发生错误: {e}")
        return

    # 3. 准备数据进行 LHS 采样
    all_cases = []
    for _, row in cases_df.iterrows():
        # 检查文件是否存在，只处理存在的文件
        # 注意：现在源文件路径是 ROWDATA_DIR
        full_path_to_csv = os.path.join(ROWDATA_DIR, row['文件名'])
        if os.path.exists(full_path_to_csv):
            all_cases.append({
                'filename': row['文件名'],
                'angles': row['角度值']
            })
        else:
            print(f"警告: 文件 '{row['文件名']}' 在 '{ROWDATA_DIR}' 中不存在，跳过此工况。")

    if not all_cases:
        print(f"未找到任何存在的文件工况，请检查 '{ROWDATA_DIR}' 文件夹内容及 'cases.csv' 中的文件名。")
        return

    num_total_cases = len(all_cases)
    # 确保测试集至少有 1 个样本（如果总数允许）
    num_test_samples = max(1, int(num_total_cases * TEST_RATIO)) 

    print(f"总工况数: {num_total_cases}。目标测试集样本数: {num_test_samples} ({TEST_RATIO*100:.0f}% of total)。")

    # 将所有工况的索引作为采样的空间
    case_indices = np.arange(num_total_cases)

    # 4. 执行拉丁超立方采样 (LHS)
    lhs_samples_norm = lhs(1, samples=num_test_samples, criterion='m').flatten()

    # 将归一化的 LHS 样本映射到实际的工况索引范围 [0, num_total_cases-1]
    selected_lhs_indices = np.round(lhs_samples_norm * (num_total_cases - 1)).astype(int)

    # 确保选中的索引是唯一的，并且都在有效范围内
    test_indices = np.unique(selected_lhs_indices)
    
    # 额外处理：如果因为 unique 导致测试集数量不足，则随机补充
    if len(test_indices) < num_test_samples:
        remaining_indices = list(set(case_indices) - set(test_indices))
        np.random.shuffle(remaining_indices) # 随机打乱剩余索引
        # 补充不足的部分
        test_indices = np.concatenate((test_indices, remaining_indices[:num_test_samples - len(test_indices)]))
        test_indices = np.unique(test_indices) # 再次确保唯一性

    # 5. 分离训练集和测试集的文件名
    test_filenames = [all_cases[i]['filename'] for i in test_indices]
    
    # 使用集合操作来高效获取训练集文件名
    all_filenames_set = set([case['filename'] for case in all_cases])
    test_filenames_set = set(test_filenames)
    train_filenames = list(all_filenames_set - test_filenames_set)

    print(f"\n开始复制文件...")
    print(f"最终测试集样本数: {len(test_filenames)}")
    print(f"最终训练集样本数: {len(train_filenames)}")

    # 6. 复制文件到相应的目录
    # 复制测试集文件
    for filename in test_filenames:
        # 源路径现在是 ROWDATA_DIR
        src_path = os.path.join(ROWDATA_DIR, filename)
        dest_path = os.path.join(TEST_DIR, filename)
        try:
            shutil.copy2(src_path, dest_path) # copy2 会复制文件和元数据
        except FileNotFoundError:
            print(f"警告: 复制测试文件时未找到源文件: {src_path}。")
        except Exception as e:
            print(f"复制测试文件 '{filename}' 时出错: {e}")

    # 复制训练集文件
    for filename in train_filenames:
        # 源路径现在是 ROWDATA_DIR
        src_path = os.path.join(ROWDATA_DIR, filename)
        dest_path = os.path.join(TRAIN_DIR, filename)
        try:
            shutil.copy2(src_path, dest_path) # copy2 会复制文件和元数据
        except FileNotFoundError:
            print(f"警告: 复制训练文件时未找到源文件: {src_path}。")
        except Exception as e:
            print(f"复制训练文件 '{filename}' 时出错: {e}")

    print("\n数据集划分并复制完成！")
    print(f"训练集文件位于: {TRAIN_DIR}")
    print(f"测试集文件位于: {TEST_DIR}")

if __name__ == "__main__":
    main()