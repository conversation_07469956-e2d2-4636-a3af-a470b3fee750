"""
数据集处理评估模块
包含网格简化质量评估和插值精度评估
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.spatial import KDTree
from sklearn.metrics import mean_squared_error, r2_score
import plotly.graph_objects as go
from pathlib import Path
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# 配置参数
BASE_PATH = 'D:/Desktop/机械臂数字孪生'
RESULT_DIR = Path(BASE_PATH) / '评估结果'
RESULT_DIR.mkdir(exist_ok=True)

def load_data():
    """加载需要评估的数据集"""
    raw_mesh = pd.read_csv('D:/Desktop/机械臂数字孪生/模型/细化r2-Jibot.csv', header=None)
    fea_data = pd.read_csv('D:/Desktop/机械臂数字孪生/多关节角度数据集/fea/test/fea_a1_-5_a2_-5_a3_-30.csv')
    processed_data = pd.read_csv('D:/Desktop/机械臂数字孪生/多关节角度数据集/data/test/data_a1_-5_a2_-5_a3_-30.csv')
    
    # 统一列名
    raw_mesh.columns = ['x', 'y', 'z']
    fea_data.columns = ['x', 'y', 'z', 'strain_fea']
    processed_data.columns = ['x', 'y', 'z', 'strain_processed']
    
    return raw_mesh, fea_data, processed_data

def evaluate_mesh_simplification(raw_mesh, processed_mesh):
    """评估网格简化质量"""
    # 计算简化率
    orig_nodes = len(raw_mesh)
    processed_nodes = len(processed_mesh)
    reduction_ratio = 1 - processed_nodes / orig_nodes
    
    # 计算几何误差
    kdtree = KDTree(processed_mesh[['x','y','z']])
    distances, _ = kdtree.query(raw_mesh[['x','y','z']])
    avg_error = np.mean(distances)
    
    # 重复节点检查
    duplicates = raw_mesh.duplicated().sum()
    
    # 可视化
    plt.figure(figsize=(12, 6))
    plt.subplot(121)
    sns.kdeplot(distances, fill=True)
    plt.title('几何误差分布')
    
    plt.subplot(122)
    plt.pie([orig_nodes, processed_nodes], 
            labels=['原始网格', '简化网格'],
            autopct='%1.1f%%')
    plt.title('节点数量对比')
    
    plt.savefig(RESULT_DIR / '网格简化评估.png')
    plt.close()
    
    return {
        '原始节点数': orig_nodes,
        '简化节点数': processed_nodes,
        '简化率': reduction_ratio,
        '平均几何误差(mm)': avg_error,
        '重复节点数': duplicates
    }

def evaluate_interpolation(fea_data, processed_data):
    """评估插值算法精度"""
    # 数据对齐
    merged = pd.merge(fea_data, processed_data, on=['x','y','z'], how='inner')
    
    # 计算指标
    rmse = np.sqrt(mean_squared_error(merged['strain_fea'], merged['strain_processed']))
    mae = np.mean(np.abs(merged['strain_fea'] - merged['strain_processed']))
    r2 = r2_score(merged['strain_fea'], merged['strain_processed'])
    
    # 可视化
    plt.figure(figsize=(12, 6))
    plt.subplot(121)
    sns.scatterplot(x='strain_fea', y='strain_processed', data=merged, alpha=0.3)
    plt.plot([0, merged['strain_fea'].max()], [0, merged['strain_fea'].max()], 'r--')
    plt.title('应变值相关性')
    
    plt.subplot(122)
    errors = merged['strain_fea'] - merged['strain_processed']
    sns.histplot(errors, kde=True)
    plt.title('误差分布')
    
    plt.savefig(RESULT_DIR / '插值精度评估.png')
    plt.close()
    
    # 3D可视化
    fig = go.Figure(data=[
        go.Scatter3d(
            x=merged['x'], y=merged['y'], z=merged['z'],
            mode='markers',
            marker=dict(
                size=3,
                color=errors,
                colorscale='RdBu',
                colorbar=dict(title='误差'),
                opacity=0.8
            )
        )
    ])
    fig.write_html(str(RESULT_DIR / '3D误差分布.html'))
    
    return {
        'RMSE': rmse,
        'MAE': mae,
        'R²': r2,
        '最大误差': errors.abs().max(),
        '异常值数量': len(merged[errors.abs() > 3*rmse])
    }

def generate_report(mesh_metrics, interp_metrics):
    """生成评估报告"""
    report = f"""
    {'='*40}
    数据集处理评估报告
    {'='*40}
    
    [网格简化质量]
    - 原始节点数: {mesh_metrics['原始节点数']}
    - 简化节点数: {mesh_metrics['简化节点数']}
    - 节点简化率: {mesh_metrics['简化率']:.2%}
    - 平均几何误差: {mesh_metrics['平均几何误差(mm)']:.4f} mm
    - 重复节点数: {mesh_metrics['重复节点数']}
    
    [插值精度]
    - RMSE: {interp_metrics['RMSE']:.6f}
    - MAE: {interp_metrics['MAE']:.6f} 
    - R²: {interp_metrics['R²']:.4f}
    - 最大绝对误差: {interp_metrics['最大误差']:.6f}
    - 异常值数量(>3σ): {interp_metrics['异常值数量']}
    
    可视化结果已保存至: {RESULT_DIR}
    """
    
    with open(RESULT_DIR / '评估报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)
        
    return report

if __name__ == "__main__":
    # 加载数据
    raw_mesh, fea_data, processed_data = load_data()
    
    # 执行评估
    mesh_metrics = evaluate_mesh_simplification(raw_mesh, processed_data)
    interp_metrics = evaluate_interpolation(fea_data, processed_data)
    
    # 生成报告
    report = generate_report(mesh_metrics, interp_metrics)
    print(report)