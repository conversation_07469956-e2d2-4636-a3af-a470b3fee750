"""
机械臂数字孪生系统 - 代理模型训练

此模块用于训练机械臂数字孪生系统的代理模型。
包括数据加载、模型训练和模型保存功能。
"""

import os
import numpy as np
import pandas as pd
import joblib
import argparse
from models import train_models


def load_data(base_path, angles, data_folder='data', column_idx=3):
    """
    加载训练或测试数据

    参数:
        base_path (str): 数据基础路径
        angles (list): 角度列表，每个元素是一个包含三个角度的元组 (a1, a2, a3)
        data_folder (str): 数据文件夹名称
        column_idx (int): 目标列索引

    返回:
        numpy.ndarray: 加载的数据，形状为 (节点数, 样本数)
    """
    # 获取网格节点数
    mesh_path = os.path.join(base_path, '无重复节点_R2_a1a2a3.csv')
    df = pd.read_csv(mesh_path, header=None)
    n_nodes = len(df)

    # 准备数据数组
    data_path = os.path.join(base_path, data_folder)
    data = np.zeros((n_nodes, len(angles)))

    # 加载每个角度组合的数据
    for idx, angle_tuple in enumerate(angles):
        a1, a2, a3 = angle_tuple  # 解包三个角度值
        file_name = f'data_a1_{int(a1)}_a2_{int(a2)}_a3_{int(a3)}.csv'
        file_path = os.path.join(data_path, file_name)
        try:
            data[:, idx] = pd.read_csv(file_path).iloc[:, column_idx].values
        except FileNotFoundError:
            print(f"警告: 文件 {file_path} 不存在，跳过")
            continue
        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {e}")
            continue

    return data


def save_model(model_list, model_type, base_path):
    """
    保存训练好的模型

    参数:
        model_list (list): 训练好的模型列表
        model_type (str): 模型类型
        base_path (str): 保存路径基础目录

    返回:
        str: 模型保存路径
    """
    # 确保模型目录存在
    model_dir = os.path.join(base_path, '模型')
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 保存模型
    model_path = os.path.join(model_dir, f'训练好的代理模型_a1a2a3_{model_type}.pkl')
    joblib.dump(model_list, model_path)

    return model_path


def main(args):
    """主函数"""
    # 设置路径
    base_path = args.base_path

    # 从CSV文件读取角度参数
    train_params_file = os.path.join(base_path, '多关节角度数据集', 'data', 'train_cases_parameters.csv')
    print(f"正在从 {train_params_file} 读取训练角度参数...")

    try:
        # 使用GBK编码读取CSV文件
        train_params_df = pd.read_csv(train_params_file, encoding='gbk')
        # 提取角度参数
        train_angles = [(row['a1'], row['a2'], row['a3']) for _, row in train_params_df.iterrows()]
        print(f"成功读取 {len(train_angles)} 组训练角度参数")
    except Exception as e:
        print(f"读取训练角度参数文件时出错: {e}")
        print("使用默认角度参数...")
        # 默认角度参数，仅在读取失败时使用
        train_angles = [
            (-90, -90, -90), (-80, -80, -80), (-70, -70, -70), (-60, -60, -60),
            (-50, -50, -50), (-40, -40, -40), (-30, -30, -30), (-20, -20, -20),
            (-10, -10, -10), (0, 0, 0), (10, 10, 10), (20, 20, 20), (30, 30, 30),
            (40, 40, 40), (50, 50, 50), (60, 60, 60), (70, 70, 70), (80, 80, 80),
            (90, 90, 90)
        ]

    # 加载训练数据
    print("正在加载训练数据...")
    Y_train = load_data(
        base_path=base_path,
        angles=train_angles,
        data_folder='多关节角度数据集\\data\\train',  # 更新为新的数据文件夹路径
        column_idx=3
    )
    print(f"训练数据加载完成，形状: {Y_train.shape}")

    # 训练模型
    print(f"开始训练 {args.model_type} 模型...")

    # 创建参数字典，排除model_type
    kwargs = {k: v for k, v in vars(args).items() if k != 'model_type'}

    # 训练模型
    model_list = train_models(
        model_type=args.model_type,
        x_train=train_angles,
        y_train=Y_train,
        **kwargs  # 传递其他命令行参数
    )
    print("模型训练完成")

    # 保存模型
    model_path = save_model(model_list, args.model_type, base_path)
    print(f"模型已保存到: {model_path}")


if __name__ == "__main__":
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='训练机械臂数字孪生系统的代理模型')

    # 基础参数
    parser.add_argument('--base_path', type=str, default='D:\\Desktop\\机械臂数字孪生',
                        help='项目基础路径')
    parser.add_argument('--model_type', type=str, default='rbf',
                        choices=['rbf', 'svr', 'tree', 'forest', 'mlp', 'torch_rbf', 'pinn_mlp'],
                        help='模型类型')

    # 模型特定参数
    parser.add_argument('--function', type=str, default='cubic',
                        help='RBF函数类型 (仅用于rbf模型)')
    # 多项式参数已移除
    parser.add_argument('--kernel', type=str, default='rbf',
                        help='SVR核函数 (仅用于svr模型)')
    parser.add_argument('--C', type=float, default=100,
                        help='SVR正则化参数 (仅用于svr模型)')
    parser.add_argument('--gamma', type=float, default=0.1,
                        help='SVR核系数 (仅用于svr模型)')
    parser.add_argument('--epsilon', type=float, default=0.1,
                        help='SVR epsilon参数 (仅用于svr模型)')
    parser.add_argument('--max_depth', type=int, default=None,
                        help='树最大深度 (用于tree和forest模型)')
    parser.add_argument('--n_estimators', type=int, default=100,
                        help='随机森林中树的数量 (仅用于forest模型)')

    # MLP模型参数
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[64, 128, 64],
                        help='MLP隐藏层维度列表 (仅用于mlp模型)')
    parser.add_argument('--dropout_rate', type=float, default=0.1,
                        help='MLP Dropout比率 (仅用于mlp模型)')
    parser.add_argument('--learning_rate', type=float, default=0.0005,
                        help='MLP和TorchRBF学习率')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='MLP和TorchRBF批次大小')
    parser.add_argument('--epochs', type=int, default=500,
                        help='MLP和TorchRBF训练轮数')

    # TorchRBF模型参数
    parser.add_argument('--n_centers', type=int, default=20,
                        help='RBF中心的数量 (仅用于torch_rbf模型)')
    parser.add_argument('--sigma', type=float, default=1.0,
                        help='RBF核的宽度参数 (仅用于torch_rbf模型)')

    # PINN-MLP模型参数
    parser.add_argument('--physics_weight', type=float, default=0.1,
                        help='物理约束权重 (仅用于pinn_mlp模型)')

    args = parser.parse_args()
    main(args)
