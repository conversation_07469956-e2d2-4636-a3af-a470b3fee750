"""
机械臂数字孪生系统 - 代理模型定义

此模块包含用于机械臂数字孪生系统的各种代理模型定义。
支持的模型类型包括：RBF、SVR、决策树、随机森林、多层感知机(MLP)和GPU加速的RBF网络(TorchRBF)。
"""

import numpy as np
import os
import datetime
import pandas as pd
import matplotlib.pyplot as plt
from scipy.interpolate import Rbf
from sklearn.tree import DecisionTreeRegressor

# PyTorch相关导入
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import TensorDataset, DataLoader

# PyTorch Geometric相关导入（用于GNN）
try:
    from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
    from torch_geometric.data import Data, Batch
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    print("Warning: torch_geometric not available. GNN model will not work.")
    TORCH_GEOMETRIC_AVAILABLE = False

# 导入PINN-MLP模型
from pinn_mlp import PINNMLPWrapper

# 导入PIRBN模型
from pirbn_model import create_pirbn_models

AVAILABLE_MODELS = ['rbf', 'tree', 'mlp', 'torch_rbf', 'pinn_mlp', 'pirbn', 'gnn', 'cnn', 'rnn', 'transformer', 'transformer_pinn']

MODEL_INITIALIZERS = {

    'transformer_pinn': lambda args: TransformerPINNWrapper(
        input_dim=3,
        output_dim=args['output_dim'],
        physics_weight=args.get('physics_weight', 0.1),
        nhead=args.get('nhead', 8),
        num_layers=args.get('num_layers', 6)
    ),
    'rbf': lambda args: Rbf(args['x_train'], args['y_train'], function='multiquadric'),
    # 'rbf': lambda args: Rbf(x_train, y_train, function='multiquadric'),
    'tree': lambda args: DecisionTreeRegressor(max_depth=5, random_state=42),
    'mlp': lambda args: MLPWrapper(input_dim=3, output_dim=args['output_dim']),
    'torch_rbf': lambda args: TorchRBFModel(n_centers=50, output_dim=args['output_dim']),
    'pinn_mlp': lambda args: PINNMLPWrapper(input_dim=3, output_dim=args['output_dim']),
    'pirbn': lambda args: create_pirbn_models(3, args['output_dim']),
    'gnn': lambda args: GNNWrapper(input_dim=3, output_dim=args['output_dim']),
    'cnn': lambda args: CNNWrapper(input_dim=3, output_dim=args['output_dim']),
    'rnn': lambda args: RNNWrapper(input_dim=3, output_dim=args['output_dim']),
    'transformer': lambda args: TransformerWrapper(input_dim=3, output_dim=args['output_dim']),
}

def save_loss_curve(losses, model_type, base_path='D:\\Desktop\\机械臂数字孪生'):
    """
    保存损失曲线数据和图像

    参数:
        losses (list): 每个epoch的损失值列表
        model_type (str): 模型类型 ('mlp' 或 'torch_rbf')
        base_path (str): 基础路径
    """
    # 获取当前日期作为文件夹名
    current_date = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 创建保存路径
    save_dir = os.path.join(base_path, '云图结果', model_type, current_date)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 保存损失值为CSV文件
    csv_path = os.path.join(save_dir, f'{model_type}_loss.csv')
    df = pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses})
    df.to_csv(csv_path, index=False)
    print(f"损失数据已保存到: {csv_path}")

    # 绘制损失曲线图
    plt.figure(figsize=(10, 6))
    plt.plot(range(1, len(losses) + 1), losses, marker='o', linestyle='-')
    plt.title(f'{model_type.upper()} Model Training Loss Curve')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)

    # 保存图像
    img_path = os.path.join(save_dir, f'{model_type}_loss_curve.png')
    plt.savefig(img_path)
    plt.close()
    print(f"损失曲线图已保存到: {img_path}")

    return save_dir





# ====================================
# 新的神经网络模型定义
# ====================================




class TransformerPINNWrapper(nn.Module):
    """
    Transformer-PINN混合模型
    特征：
    - 基于Transformer编码器架构
    - 添加简化的应变平衡方程作为物理约束项
    - 总损失函数L = L_mse + λ*L_physics
    """

    def __init__(self, input_dim=3, output_dim=2606, physics_weight=0.1, nhead=8, num_layers=6):
        super().__init__()
        self.physics_weight = physics_weight
        
        # Transformer编码器层
        self.encoder_layer = nn.TransformerEncoderLayer(
            d_model=input_dim,
            nhead=nhead,
            dim_feedforward=512
        )
        self.transformer_encoder = nn.TransformerEncoder(self.encoder_layer, num_layers=num_layers)
        
        # 全连接输出层
        self.fc_out = nn.Linear(input_dim, output_dim)
        
        # 材料参数（示例值，需根据实际调整）
        self.E = 2.1e5  # 弹性模量

    def forward(self, x, y_true=None):
        # 输入维度转换 [batch_size, 3] -> [batch_size, 1, 3]
        x = x.unsqueeze(1)
        
        # Transformer编码
        encoded = self.transformer_encoder(x)
        encoded = encoded.squeeze(1)
        
        # 预测应变
        pred = self.fc_out(encoded)
        
        if y_true is not None:
            # 数据损失
            loss_mse = F.mse_loss(pred, y_true)
            
            # 物理约束损失（简化应变平衡方程）
            strain = pred / self.E  # 应变ε = σ/E
            
            # 自动微分计算应变梯度
            grad_x = torch.autograd.grad(
                outputs=strain,
                inputs=x,
                grad_outputs=torch.ones_like(strain),
                create_graph=True,
                retain_graph=True
            )[0]
            
            # 简化平衡方程：∇·σ + F = 0 → 这里假设F=0
            residual = torch.mean(torch.sum(grad_x, dim=1)**2)
            
            # 总损失
            total_loss = loss_mse + self.physics_weight * residual
            return pred, total_loss
            
        return pred


class GNNModel(nn.Module):
    """图神经网络模型，用于处理机械臂节点间的空间关系"""

    def __init__(self, input_dim=3, hidden_dim=64, output_dim=2606, num_layers=3, dropout_rate=0.2):
        super(GNNModel, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers

        if not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("torch_geometric is required for GNN model")

        # 图卷积层
        self.convs = nn.ModuleList()
        self.convs.append(GCNConv(input_dim, hidden_dim))
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))
        self.convs.append(GCNConv(hidden_dim, hidden_dim))

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim * 2, output_dim)
        )

        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, x, edge_index, batch=None):
        # 图卷积
        for i, conv in enumerate(self.convs):
            x = conv(x, edge_index)
            if i < len(self.convs) - 1:
                x = F.relu(x)
                x = self.dropout(x)

        # 全局池化
        if batch is not None:
            x = global_mean_pool(x, batch)
        else:
            x = torch.mean(x, dim=0, keepdim=True)

        # 输出层
        return self.output_layer(x)


class CNNModel(nn.Module):
    """1D卷积神经网络模型，用于处理序列化的角度参数"""

    def __init__(self, input_dim=3, hidden_dims=[64, 128, 256], output_dim=2606,
                 kernel_size=3, dropout_rate=0.2):
        super(CNNModel, self).__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim

        # 输入嵌入层（将3个角度扩展为更长的序列）
        self.input_embedding = nn.Linear(input_dim, 64)

        # 1D卷积层
        self.conv_layers = nn.ModuleList()
        in_channels = 1
        for hidden_dim in hidden_dims:
            self.conv_layers.append(nn.Sequential(
                nn.Conv1d(in_channels, hidden_dim, kernel_size, padding=kernel_size//2),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ))
            in_channels = hidden_dim

        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dims[-1], 512),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(512, output_dim)
        )

    def forward(self, x):
        # 输入嵌入
        x = self.input_embedding(x)  # (batch_size, 64)
        x = x.unsqueeze(1)  # (batch_size, 1, 64) 为1D卷积添加通道维度

        # 1D卷积
        for conv_layer in self.conv_layers:
            x = conv_layer(x)

        # 全局池化
        x = self.global_pool(x)  # (batch_size, hidden_dims[-1], 1)
        x = x.squeeze(-1)  # (batch_size, hidden_dims[-1])

        # 输出层
        return self.output_layer(x)


class RNNModel(nn.Module):
    """循环神经网络模型，使用LSTM处理序列数据"""

    def __init__(self, input_dim=3, hidden_dim=128, output_dim=2606,
                 num_layers=2, dropout_rate=0.2, rnn_type='LSTM'):
        super(RNNModel, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.rnn_type = rnn_type

        # 输入嵌入层（将3个角度扩展为序列）
        self.input_embedding = nn.Linear(input_dim, 32)
        self.sequence_length = 16  # 将输入扩展为16步的序列

        # RNN层
        if rnn_type == 'LSTM':
            self.rnn = nn.LSTM(32, hidden_dim, num_layers,
                              batch_first=True, dropout=dropout_rate if num_layers > 1 else 0)
        elif rnn_type == 'GRU':
            self.rnn = nn.GRU(32, hidden_dim, num_layers,
                             batch_first=True, dropout=dropout_rate if num_layers > 1 else 0)
        else:
            raise ValueError(f"Unsupported RNN type: {rnn_type}")

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim * 2, output_dim)
        )

    def forward(self, x):
        batch_size = x.size(0)

        # 输入嵌入
        x = self.input_embedding(x)  # (batch_size, 32)

        # 扩展为序列
        x = x.unsqueeze(1).repeat(1, self.sequence_length, 1)  # (batch_size, seq_len, 32)

        # RNN处理
        if self.rnn_type == 'LSTM':
            output, (hidden, cell) = self.rnn(x)
            # 使用最后一个时间步的输出
            x = output[:, -1, :]  # (batch_size, hidden_dim)
        else:  # GRU
            output, hidden = self.rnn(x)
            x = output[:, -1, :]  # (batch_size, hidden_dim)

        # 输出层
        return self.output_layer(x)


class TransformerModel(nn.Module):
    """Transformer模型，使用自注意力机制处理输入"""

    def __init__(self, input_dim=3, d_model=128, nhead=8, num_layers=6,
                 output_dim=2606, dropout_rate=0.1):
        super(TransformerModel, self).__init__()
        self.input_dim = input_dim
        self.d_model = d_model
        self.output_dim = output_dim

        # 输入嵌入层
        self.input_embedding = nn.Linear(input_dim, d_model)
        self.sequence_length = 16  # 将输入扩展为16步的序列

        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(self.sequence_length, d_model))

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout_rate,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(d_model * 2, output_dim)
        )

    def forward(self, x):
        batch_size = x.size(0)

        # 输入嵌入
        x = self.input_embedding(x)  # (batch_size, d_model)

        # 扩展为序列并添加位置编码
        x = x.unsqueeze(1).repeat(1, self.sequence_length, 1)  # (batch_size, seq_len, d_model)
        x = x + self.pos_encoding.unsqueeze(0)  # 添加位置编码

        # Transformer编码
        x = self.transformer(x)  # (batch_size, seq_len, d_model)

        # 使用平均池化聚合序列信息
        x = torch.mean(x, dim=1)  # (batch_size, d_model)

        # 输出层
        return self.output_layer(x)

# ====================================
# 新的神经网络模型包装类
# ====================================

class GNNWrapper:
    """GNN模型的包装类，用于训练和预测"""

    def __init__(self, input_dim=3, hidden_dim=64, output_dim=2606, num_layers=3,
                 dropout_rate=0.2, learning_rate=0.001, batch_size=32, epochs=200, device=None, **kwargs):
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = GNNModel(input_dim, hidden_dim, output_dim, num_layers, dropout_rate).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

        # 图结构相关
        self.edge_index = None
        self._build_graph_structure()

    def _build_graph_structure(self):
        """构建图结构（简单的网格连接）"""
        # 这里创建一个简单的图结构，实际应用中可以根据节点坐标构建更复杂的图
        num_nodes = self.model.output_dim
        edges = []

        # 创建一个简单的链式图结构
        for i in range(num_nodes - 1):
            edges.append([i, i + 1])
            edges.append([i + 1, i])

        # 添加一些跳跃连接
        for i in range(0, num_nodes - 2, 2):
            edges.append([i, i + 2])
            edges.append([i + 2, i])

        self.edge_index = torch.tensor(edges, dtype=torch.long).t().contiguous().to(self.device)

    def train(self, x_train, y_train):
        """训练模型"""
        # 数据预处理
        if len(x_train.shape) == 1:
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            x_train = x_train.reshape(-1, 3)

        y_train_transposed = y_train.T

        # 数据标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)
        x_std[x_std == 0] = 1
        x_normalized = (x_train - x_mean) / x_std

        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)
        y_std[y_std == 0] = 1
        y_normalized = (y_train_transposed - y_mean) / y_std

        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50

        from tqdm import tqdm
        losses = []
        progress_bar = tqdm(range(self.epochs), desc="训练GNN模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            for inputs, targets in dataloader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                # 为每个样本创建图数据
                batch_size = inputs.size(0)
                num_nodes = self.model.output_dim

                # 扩展输入到所有节点
                node_features = inputs.unsqueeze(1).repeat(1, num_nodes, 1)  # (batch_size, num_nodes, input_dim)
                node_features = node_features.view(-1, inputs.size(-1))  # (batch_size * num_nodes, input_dim)

                # 创建批次信息
                batch = torch.arange(batch_size).repeat_interleave(num_nodes).to(self.device)

                # 调整边索引以适应批次
                edge_index_batch = []
                for i in range(batch_size):
                    offset = i * num_nodes
                    edge_index_batch.append(self.edge_index + offset)
                edge_index_batch = torch.cat(edge_index_batch, dim=1)

                self.optimizer.zero_grad()
                outputs = self.model(node_features, edge_index_batch, batch)
                loss = self.criterion(outputs, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            losses.append(avg_loss)
            self.scheduler.step(avg_loss)

            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            if avg_loss < best_loss * 0.995:
                best_loss = avg_loss
                patience_counter = 0
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    self.model.load_state_dict(best_model_state)
                    break

        self._save_loss_curve(losses, 'gnn')

    def predict(self, x):
        """预测 - 优化内存使用"""
        self.model.eval()

        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            # 对于大批次，使用分批处理避免内存溢出
            batch_size = x_normalized.shape[0]
            max_batch_size = 8  # 限制最大批次大小以避免内存问题

            if batch_size > max_batch_size:
                # 分批处理
                results = []
                for i in range(0, batch_size, max_batch_size):
                    end_idx = min(i + max_batch_size, batch_size)
                    batch_x = x_normalized[i:end_idx]
                    batch_result = self._predict_small_batch(batch_x)
                    results.append(batch_result)
                y_pred = np.vstack(results)
            else:
                y_pred = self._predict_small_batch(x_normalized)

            return y_pred
        else:
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    x = x[0]
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                num_nodes = self.model.output_dim

                # 扩展输入到所有节点
                node_features = x_tensor.repeat(num_nodes, 1)

                y_normalized = self.model(node_features, self.edge_index, None).cpu().numpy().flatten()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred

    def _predict_small_batch(self, x_normalized):
        """处理小批次预测，避免内存溢出"""
        with torch.no_grad():
            x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
            batch_size = x_tensor.size(0)
            num_nodes = self.model.output_dim

            # 扩展输入到所有节点
            node_features = x_tensor.unsqueeze(1).repeat(1, num_nodes, 1)
            node_features = node_features.view(-1, x_tensor.size(-1))

            # 创建批次信息
            batch = torch.arange(batch_size).repeat_interleave(num_nodes).to(self.device)

            # 调整边索引
            edge_index_batch = []
            for i in range(batch_size):
                offset = i * num_nodes
                edge_index_batch.append(self.edge_index + offset)
            edge_index_batch = torch.cat(edge_index_batch, dim=1)

            y_normalized = self.model(node_features, edge_index_batch, batch).cpu().numpy()

        y_pred = y_normalized * self.y_std + self.y_mean
        return y_pred

    def _save_loss_curve(self, losses, model_name):
        """保存损失曲线"""
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        base_path = 'D:\\Desktop\\机械臂数字孪生'
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")


class CNNWrapper:
    """CNN模型的包装类，用于训练和预测"""

    def __init__(self, input_dim=3, hidden_dims=[64, 128, 256], output_dim=2606,
                 kernel_size=3, dropout_rate=0.2, learning_rate=0.001, batch_size=32, epochs=200, device=None, **kwargs):
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = CNNModel(input_dim, hidden_dims, output_dim, kernel_size, dropout_rate).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

    def train(self, x_train, y_train):
        """训练模型"""
        # 数据预处理（与其他模型相同的逻辑）
        if len(x_train.shape) == 1:
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            x_train = x_train.reshape(-1, 3)

        y_train_transposed = y_train.T

        # 数据标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)
        x_std[x_std == 0] = 1
        x_normalized = (x_train - x_mean) / x_std

        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)
        y_std[y_std == 0] = 1
        y_normalized = (y_train_transposed - y_mean) / y_std

        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50

        from tqdm import tqdm
        losses = []
        progress_bar = tqdm(range(self.epochs), desc="训练CNN模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            for inputs, targets in dataloader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            losses.append(avg_loss)
            self.scheduler.step(avg_loss)

            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            if avg_loss < best_loss * 0.995:
                best_loss = avg_loss
                patience_counter = 0
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    self.model.load_state_dict(best_model_state)
                    break

        self._save_loss_curve(losses, 'cnn')

    def predict(self, x):
        """预测"""
        self.model.eval()

        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred
        else:
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    x = x[0]
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy().flatten()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred

    def _save_loss_curve(self, losses, model_name):
        """保存损失曲线"""
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        base_path = 'D:\\Desktop\\机械臂数字孪生'
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")

class RNNWrapper:
    """RNN模型的包装类，用于训练和预测"""

    def __init__(self, input_dim=3, hidden_dim=128, output_dim=2606, num_layers=2,
                 dropout_rate=0.2, rnn_type='LSTM', learning_rate=0.001, batch_size=32, epochs=200, device=None, **kwargs):
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = RNNModel(input_dim, hidden_dim, output_dim, num_layers, dropout_rate, rnn_type).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

    def train(self, x_train, y_train):
        """训练模型"""
        # 数据预处理
        if len(x_train.shape) == 1:
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            x_train = x_train.reshape(-1, 3)

        y_train_transposed = y_train.T

        # 数据标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)
        x_std[x_std == 0] = 1
        x_normalized = (x_train - x_mean) / x_std

        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)
        y_std[y_std == 0] = 1
        y_normalized = (y_train_transposed - y_mean) / y_std

        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50

        from tqdm import tqdm
        losses = []
        progress_bar = tqdm(range(self.epochs), desc="训练RNN模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            for inputs, targets in dataloader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            losses.append(avg_loss)
            self.scheduler.step(avg_loss)

            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            if avg_loss < best_loss * 0.995:
                best_loss = avg_loss
                patience_counter = 0
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    self.model.load_state_dict(best_model_state)
                    break

        self._save_loss_curve(losses, 'rnn')

    def predict(self, x):
        """预测"""
        self.model.eval()

        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred
        else:
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    x = x[0]
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy().flatten()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred

    def _save_loss_curve(self, losses, model_name):
        """保存损失曲线"""
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        base_path = 'D:\\Desktop\\机械臂数字孪生'
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")


class TransformerWrapper:
    """Transformer模型的包装类，用于训练和预测"""

    def __init__(self, input_dim=3, d_model=128, nhead=8, num_layers=6, output_dim=2606,
                 dropout_rate=0.1, learning_rate=0.001, batch_size=32, epochs=200, device=None, **kwargs):
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = TransformerModel(input_dim, d_model, nhead, num_layers, output_dim, dropout_rate).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

    def train(self, x_train, y_train):
        """训练模型"""
        # 数据预处理
        if len(x_train.shape) == 1:
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            x_train = x_train.reshape(-1, 3)

        y_train_transposed = y_train.T

        # 数据标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)
        x_std[x_std == 0] = 1
        x_normalized = (x_train - x_mean) / x_std

        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)
        y_std[y_std == 0] = 1
        y_normalized = (y_train_transposed - y_mean) / y_std

        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50

        from tqdm import tqdm
        losses = []
        progress_bar = tqdm(range(self.epochs), desc="训练Transformer模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            for inputs, targets in dataloader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            losses.append(avg_loss)
            self.scheduler.step(avg_loss)

            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            if avg_loss < best_loss * 0.995:
                best_loss = avg_loss
                patience_counter = 0
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    self.model.load_state_dict(best_model_state)
                    break

        self._save_loss_curve(losses, 'transformer')

    def predict(self, x):
        """预测"""
        self.model.eval()

        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred
        else:
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    x = x[0]
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            x_array = np.array(x)
            x_normalized = (x_array - self.x_mean) / self.x_std

            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy().flatten()

            y_pred = y_normalized * self.y_std + self.y_mean
            return y_pred

    def _save_loss_curve(self, losses, model_name):
        """保存损失曲线"""
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        base_path = 'D:\\Desktop\\机械臂数字孪生'
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")

class MLPModel(nn.Module):
    """多层感知机模型，带有残差连接和dropout层，一个模型预测所有节点"""

    def __init__(self, input_dim=3, hidden_dims=[64, 128, 64], output_dim=1, dropout_rate=0.1):
        """
        初始化MLP模型

        参数:
            input_dim (int): 输入维度，默认为3（三个关节角度值）
            hidden_dims (list): 隐藏层维度列表
            output_dim (int): 输出维度，等于节点数量（所有节点的应变值）
            dropout_rate (float): Dropout比率，用于防止过拟合
        """
        super(MLPModel, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dims = hidden_dims

        # 输入标准化层
        self.input_bn = nn.BatchNorm1d(input_dim)

        # 构建网络层
        layers = []

        # 输入层 -> 第一个隐藏层
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        layers.append(nn.BatchNorm1d(hidden_dims[0]))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))

        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            layers.append(nn.BatchNorm1d(hidden_dims[i+1]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))

        # 输出层
        layers.append(nn.Linear(hidden_dims[-1], output_dim))

        # 将所有层组合成一个Sequential模型
        self.main_layers = nn.Sequential(*layers)

        # 残差连接（从输入直接到输出的快捷连接）
        self.residual = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.ReLU(),
            nn.Linear(hidden_dims[0], output_dim),
            nn.BatchNorm1d(output_dim)
        )

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """前向传播"""
        # 输入标准化
        x = self.input_bn(x)

        # 主要网络路径
        main_output = self.main_layers(x)

        # 残差连接
        res_output = self.residual(x)

        # 组合主输出和残差连接
        return main_output + res_output

    def predict(self, x):
        """
        预测所有节点的应变值

        参数:
            x (tuple或numpy.ndarray): 输入角度值，可以是单个元组(a1,a2,a3)或包含多个元组的数组

        返回:
            numpy.ndarray: 所有节点的预测应变值
        """
        with torch.no_grad():
            # 检查输入类型
            if isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1:
                # 批量预测 - 多个样本
                x_tensor = torch.tensor(x, dtype=torch.float32)
                return self.forward(x_tensor).cpu().numpy()
            else:
                # 单个预测 - 一个样本
                if isinstance(x, (list, tuple, np.ndarray)):
                    if len(np.shape(x)) > 1:
                        # 如果是二维数组但只有一个样本，取第一个元素
                        x = x[0]
                    # 确保x是一个包含三个角度的元组或数组
                    if len(x) != self.input_dim:
                        raise ValueError(f"输入维度不匹配，期望{self.input_dim}个角度值，但得到{len(x)}个")

                x_tensor = torch.tensor(x, dtype=torch.float32).view(1, -1)
                return self.forward(x_tensor).cpu().numpy().flatten()


class MLPWrapper:
    """MLP模型的包装类，用于训练和预测所有节点的应变值"""

    def __init__(self, input_dim=3, hidden_dims=[64, 128, 64], output_dim=1, dropout_rate=0.1,
                 learning_rate=0.0005, batch_size=64, epochs=200, device=None):
        """
        初始化MLP包装器

        参数:
            input_dim (int): 输入维度，默认为3（三个关节角度值）
            hidden_dims (list): 隐藏层维度列表
            output_dim (int): 输出维度（节点数量）
            dropout_rate (float): Dropout比率
            learning_rate (float): 学习率
            batch_size (int): 批次大小
            epochs (int): 训练轮数
            device (str): 训练设备 ('cuda' 或 'cpu')
        """
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = MLPModel(input_dim, hidden_dims, output_dim, dropout_rate).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

    def train(self, x_train, y_train):
        """
        训练模型并记录损失曲线

        参数:
            x_train (numpy.ndarray): 训练输入数据（角度值），形状为 (样本数, 3)，每行包含三个关节角度
            y_train (numpy.ndarray): 训练目标数据（所有节点的应变值），形状为 (节点数, 样本数)
        """
        # 确保输入数据是正确的形状
        if len(x_train.shape) == 1:
            # 如果是一维数组，假设只有一个样本，重塑为(1, 3)
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            # 如果是高维数组，尝试重塑为(样本数, 3)
            x_train = x_train.reshape(-1, 3)

        # 转置y_train，使其形状为 (样本数, 节点数)
        y_train_transposed = y_train.T

        # 数据标准化 - 对每个角度维度分别标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)

        # 防止除以零
        x_std[x_std == 0] = 1

        x_normalized = (x_train - x_mean) / x_std

        # 对每个节点的输出进行标准化
        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)

        # 防止除以零
        y_std[y_std == 0] = 1

        y_normalized = (y_train_transposed - y_mean) / y_std

        # 保存标准化参数，用于预测时的反标准化
        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50  # 增加早停的耐心值，允许模型训练更长时间

        # 导入tqdm库用于显示进度条
        from tqdm import tqdm

        # 创建损失列表用于记录每个epoch的损失
        losses = []

        # 使用tqdm创建进度条
        progress_bar = tqdm(range(self.epochs), desc="训练MLP模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            batch_progress = tqdm(dataloader, desc=f"Epoch {epoch+1}/{self.epochs}", leave=False, ncols=100)

            for inputs, targets in batch_progress:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                # 反向传播和优化
                loss.backward()

                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                total_loss += loss.item()
                # 更新批次进度条的描述，显示当前批次的损失
                batch_progress.set_postfix(loss=f"{loss.item():.4f}")

            # 计算平均损失
            avg_loss = total_loss / len(dataloader)

            # 记录损失
            losses.append(avg_loss)

            # 学习率调度器 - 减小factor，使学习率下降更缓慢
            self.scheduler.step(avg_loss)

            # 更新主进度条的描述，显示当前epoch的平均损失
            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            # 早停检查 - 使用相对改进而不是绝对改进
            if avg_loss < best_loss * 0.995:  # 只有当损失下降至少0.5%时才更新最佳模型
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
                # 在进度条中显示找到了新的最佳模型
                progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}", best="True")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    # 恢复最佳模型
                    self.model.load_state_dict(best_model_state)
                    break

        # 保存损失曲线
        self._save_loss_curve(losses, 'mlp')

    def predict(self, x):
        """
        预测所有节点的应变值

        参数:
            x (tuple或numpy.ndarray): 输入角度值，可以是单个元组(a1,a2,a3)或包含多个元组的数组

        返回:
            numpy.ndarray: 所有节点的预测应变值
        """
        self.model.eval()

        # 检查是否是批量预测
        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            # 批量预测 - 多个样本
            # 确保x是numpy数组
            x_array = np.array(x)

            # 标准化输入 - 对每个角度维度分别标准化
            x_normalized = (x_array - self.x_mean) / self.x_std

            # 预测
            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy()

            # 反标准化输出 - 对每个样本应用相同的变换
            y_pred = y_normalized * self.y_std + self.y_mean

            return y_pred
        else:
            # 单个预测 - 一个样本
            # 确保x是正确的形状
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    # 如果是二维数组但只有一个样本，取第一个元素
                    x = x[0]
                # 确保x是一个包含三个角度的元组或数组
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            # 转换为numpy数组
            x_array = np.array(x)

            # 标准化输入
            x_normalized = (x_array - self.x_mean) / self.x_std

            # 预测
            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy().flatten()

            # 反标准化输出
            y_pred = y_normalized * self.y_std + self.y_mean

            return y_pred

    def save(self, path):
        """保存模型到文件"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'input_dim': self.model.input_dim,
            'hidden_dims': self.model.hidden_dims,
            'output_dim': self.model.output_dim,
            'x_mean': self.x_mean.tolist() if isinstance(self.x_mean, np.ndarray) else self.x_mean,
            'x_std': self.x_std.tolist() if isinstance(self.x_std, np.ndarray) else self.x_std,
            'y_mean': self.y_mean.tolist() if self.y_mean is not None else None,
            'y_std': self.y_std.tolist() if self.y_std is not None else None
        }, path)

    def _save_loss_curve(self, losses, model_name):
        """
        保存损失曲线到文件

        参数:
            losses (list): 训练过程中的损失值列表
            model_name (str): 模型名称
        """
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        # 创建保存目录
        base_path = 'D:\\Desktop\\机械臂数字孪生'  # 使用默认路径
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        # 获取当前日期时间作为文件名的一部分
        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        # 保存图表
        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        # 保存损失数据到CSV文件
        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")

    def load(self, path):
        """从文件加载模型"""
        checkpoint = torch.load(path)

        # 加载模型参数
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

        # 加载标准化参数
        x_mean = checkpoint.get('x_mean', 0)
        x_std = checkpoint.get('x_std', 1)

        # 确保x_mean和x_std是numpy数组
        if isinstance(x_mean, list):
            self.x_mean = np.array(x_mean)
        else:
            self.x_mean = x_mean

        if isinstance(x_std, list):
            self.x_std = np.array(x_std)
        else:
            self.x_std = x_std

        # 加载y的标准化参数，并转换为numpy数组
        y_mean = checkpoint.get('y_mean')
        y_std = checkpoint.get('y_std')

        if y_mean is not None:
            self.y_mean = np.array(y_mean)
        else:
            self.y_mean = None

        if y_std is not None:
            self.y_std = np.array(y_std)
        else:
            self.y_std = None


class TorchRBFModel(nn.Module):
    """使用PyTorch实现的RBF网络模型，支持GPU加速"""

    def __init__(self, n_centers, output_dim=1, sigma=1.0, input_dim=3):
        """
        初始化RBF网络模型

        参数:
            n_centers (int): RBF中心的数量
            output_dim (int): 输出维度，等于节点数量（所有节点的应变值）
            sigma (float): RBF核的宽度参数
            input_dim (int): 输入维度，默认为3（三个关节角度值）
        """
        super(TorchRBFModel, self).__init__()

        self.n_centers = n_centers
        self.output_dim = output_dim
        self.sigma = sigma
        self.input_dim = input_dim

        # RBF中心，初始化为均匀分布在[-1, 1]之间
        self.centers = nn.Parameter(torch.Tensor(n_centers, input_dim))
        self.reset_parameters()

        # 输出层权重
        self.linear = nn.Linear(n_centers, output_dim)

        # 批归一化层
        self.bn = nn.BatchNorm1d(input_dim)

    def reset_parameters(self):
        """初始化RBF中心"""
        nn.init.uniform_(self.centers, -1, 1)

    def rbf(self, x):
        """RBF激活函数"""
        # x形状: [batch_size, input_dim]
        # centers形状: [n_centers, input_dim]
        # 计算每个输入点到每个中心的距离
        # 输出形状: [batch_size, n_centers]

        # 将x扩展为[batch_size, 1, input_dim]
        x_expanded = x.unsqueeze(1)

        # 将centers扩展为[1, n_centers, input_dim]
        centers = self.centers.unsqueeze(0)

        # 计算欧氏距离的平方
        distances = torch.sum((x_expanded - centers) ** 2, dim=2)

        # 应用RBF核函数
        return torch.exp(-distances / (2 * self.sigma ** 2))

    def forward(self, x):
        """前向传播"""
        # 输入标准化
        x = self.bn(x)

        # 应用RBF激活函数
        rbf_out = self.rbf(x)

        # 线性输出层
        output = self.linear(rbf_out)

        return output

    def predict(self, x):
        """
        预测所有节点的应变值

        参数:
            x (tuple或numpy.ndarray): 输入角度值，可以是单个元组(a1,a2,a3)或包含多个元组的数组

        返回:
            numpy.ndarray: 所有节点的预测应变值
        """
        with torch.no_grad():
            # 检查输入类型
            if isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1:
                # 批量预测 - 多个样本
                x_tensor = torch.tensor(x, dtype=torch.float32)
                return self.forward(x_tensor).cpu().numpy()
            else:
                # 单个预测 - 一个样本
                if isinstance(x, (list, tuple, np.ndarray)):
                    if len(np.shape(x)) > 1:
                        # 如果是二维数组但只有一个样本，取第一个元素
                        x = x[0]
                    # 确保x是一个包含三个角度的元组或数组
                    if len(x) != self.input_dim:
                        raise ValueError(f"输入维度不匹配，期望{self.input_dim}个角度值，但得到{len(x)}个")

                x_tensor = torch.tensor(x, dtype=torch.float32).view(1, -1)
                return self.forward(x_tensor).cpu().numpy().flatten()


class TorchRBFWrapper:
    """TorchRBF模型的包装类，用于训练和预测所有节点的应变值"""

    def __init__(self, n_centers=20, output_dim=1, sigma=1.0, input_dim=3,
                 learning_rate=0.001, batch_size=64, epochs=200, device=None):
        """
        初始化TorchRBF包装器

        参数:
            n_centers (int): RBF中心的数量
            output_dim (int): 输出维度（节点数量）
            sigma (float): RBF核的宽度参数
            input_dim (int): 输入维度，默认为3（三个关节角度值）
            learning_rate (float): 学习率
            batch_size (int): 批次大小
            epochs (int): 训练轮数
            device (str): 训练设备 ('cuda' 或 'cpu')
        """
        # 确定设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 创建模型
        self.model = TorchRBFModel(n_centers, output_dim, sigma, input_dim).to(self.device)

        # 设置优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=20, verbose=True, min_lr=1e-6
        )

        # 训练参数
        self.batch_size = batch_size
        self.epochs = epochs

        # 标准化参数
        self.x_mean = 0
        self.x_std = 1
        self.y_mean = None
        self.y_std = None

    def train(self, x_train, y_train):
        """
        训练模型并记录损失曲线

        参数:
            x_train (numpy.ndarray): 训练输入数据（角度值），形状为 (样本数, 3)，每行包含三个关节角度
            y_train (numpy.ndarray): 训练目标数据（所有节点的应变值），形状为 (节点数, 样本数)
        """
        # 确保输入数据是正确的形状
        if len(x_train.shape) == 1:
            # 如果是一维数组，假设只有一个样本，重塑为(1, 3)
            x_train = x_train.reshape(1, -1)
        elif len(x_train.shape) > 2:
            # 如果是高维数组，尝试重塑为(样本数, 3)
            x_train = x_train.reshape(-1, 3)

        # 转置y_train，使其形状为 (样本数, 节点数)
        y_train_transposed = y_train.T

        # 数据标准化 - 对每个角度维度分别标准化
        x_mean = np.mean(x_train, axis=0)
        x_std = np.std(x_train, axis=0)

        # 防止除以零
        x_std[x_std == 0] = 1

        x_normalized = (x_train - x_mean) / x_std

        # 对每个节点的输出进行标准化
        y_mean = np.mean(y_train_transposed, axis=0)
        y_std = np.std(y_train_transposed, axis=0)

        # 防止除以零
        y_std[y_std == 0] = 1

        y_normalized = (y_train_transposed - y_mean) / y_std

        # 保存标准化参数，用于预测时的反标准化
        self.x_mean, self.x_std = x_mean, x_std
        self.y_mean, self.y_std = y_mean, y_std

        # 转换为PyTorch张量
        x_tensor = torch.tensor(x_normalized, dtype=torch.float32)
        y_tensor = torch.tensor(y_normalized, dtype=torch.float32)

        # 创建数据集和数据加载器
        dataset = TensorDataset(x_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        patience = 50  # 增加早停的耐心值，允许模型训练更长时间

        # 导入tqdm库用于显示进度条
        from tqdm import tqdm

        # 创建损失列表用于记录每个epoch的损失
        losses = []

        # 使用tqdm创建进度条
        progress_bar = tqdm(range(self.epochs), desc="训练TorchRBF模型", ncols=100)

        for epoch in progress_bar:
            total_loss = 0
            batch_progress = tqdm(dataloader, desc=f"Epoch {epoch+1}/{self.epochs}", leave=False, ncols=100)

            for inputs, targets in batch_progress:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                loss = self.criterion(outputs, targets)

                # 反向传播和优化
                loss.backward()

                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                total_loss += loss.item()
                # 更新批次进度条的描述，显示当前批次的损失
                batch_progress.set_postfix(loss=f"{loss.item():.4f}")

            # 计算平均损失
            avg_loss = total_loss / len(dataloader)

            # 记录损失
            losses.append(avg_loss)

            # 学习率调度器 - 减小factor，使学习率下降更缓慢
            self.scheduler.step(avg_loss)

            # 更新主进度条的描述，显示当前epoch的平均损失
            progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}")

            # 早停检查 - 使用相对改进而不是绝对改进
            if avg_loss < best_loss * 0.999:  # 只有当损失下降至少0.1%时才更新最佳模型
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                best_model_state = {k: v.cpu().detach() for k, v in self.model.state_dict().items()}
                # 在进度条中显示找到了新的最佳模型
                progress_bar.set_postfix(avg_loss=f"{avg_loss:.4f}", lr=f"{self.optimizer.param_groups[0]['lr']:.6f}", best="True")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    progress_bar.write(f'Early stopping at epoch {epoch+1}')
                    # 恢复最佳模型
                    self.model.load_state_dict(best_model_state)
                    break

        # 保存损失曲线
        self._save_loss_curve(losses, 'torch_rbf')

    def predict(self, x):
        """
        预测所有节点的应变值

        参数:
            x (tuple或numpy.ndarray): 输入角度值，可以是单个元组(a1,a2,a3)或包含多个元组的数组

        返回:
            numpy.ndarray: 所有节点的预测应变值
        """
        self.model.eval()

        # 检查是否是批量预测
        is_batch = isinstance(x, (list, tuple, np.ndarray)) and len(np.shape(x)) > 1 and len(x) > 1

        if is_batch:
            # 批量预测 - 多个样本
            # 确保x是numpy数组
            x_array = np.array(x)

            # 标准化输入 - 对每个角度维度分别标准化
            x_normalized = (x_array - self.x_mean) / self.x_std

            # 预测
            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy()

            # 反标准化输出 - 对每个样本应用相同的变换
            y_pred = y_normalized * self.y_std + self.y_mean

            return y_pred
        else:
            # 单个预测 - 一个样本
            # 确保x是正确的形状
            if isinstance(x, (list, tuple, np.ndarray)):
                if len(np.shape(x)) > 1:
                    # 如果是二维数组但只有一个样本，取第一个元素
                    x = x[0]
                # 确保x是一个包含三个角度的元组或数组
                if len(x) != self.model.input_dim:
                    raise ValueError(f"输入维度不匹配，期望{self.model.input_dim}个角度值，但得到{len(x)}个")

            # 转换为numpy数组
            x_array = np.array(x)

            # 标准化输入
            x_normalized = (x_array - self.x_mean) / self.x_std

            # 预测
            with torch.no_grad():
                x_tensor = torch.tensor(x_normalized, dtype=torch.float32).view(1, -1).to(self.device)
                y_normalized = self.model(x_tensor).cpu().numpy().flatten()

            # 反标准化输出
            y_pred = y_normalized * self.y_std + self.y_mean

            return y_pred

    def save(self, path):
        """保存模型到文件"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'n_centers': self.model.n_centers,
            'output_dim': self.model.output_dim,
            'sigma': self.model.sigma,
            'input_dim': self.model.input_dim,
            'x_mean': self.x_mean.tolist() if isinstance(self.x_mean, np.ndarray) else self.x_mean,
            'x_std': self.x_std.tolist() if isinstance(self.x_std, np.ndarray) else self.x_std,
            'y_mean': self.y_mean.tolist() if self.y_mean is not None else None,
            'y_std': self.y_std.tolist() if self.y_std is not None else None
        }, path)

    def _save_loss_curve(self, losses, model_name):
        """
        保存损失曲线到文件

        参数:
            losses (list): 训练过程中的损失值列表
            model_name (str): 模型名称
        """
        import matplotlib.pyplot as plt
        import os
        from datetime import datetime

        # 创建保存目录
        base_path = 'D:\\Desktop\\机械臂数字孪生'  # 使用默认路径
        save_folder = os.path.join(base_path, '云图结果', model_name, '损失曲线')
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)

        # 获取当前日期时间作为文件名的一部分
        now = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.plot(losses)
        plt.title(f'{model_name.upper()} Model Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.grid(True)

        # 保存图表
        save_path = os.path.join(save_folder, f'{model_name}_loss_curve_{now}.png')
        plt.savefig(save_path)
        plt.close()

        # 保存损失数据到CSV文件
        import pandas as pd
        csv_path = os.path.join(save_folder, f'{model_name}_loss_data_{now}.csv')
        pd.DataFrame({'epoch': range(1, len(losses) + 1), 'loss': losses}).to_csv(csv_path, index=False)

        print(f"Loss curve saved to: {save_path}")
        print(f"Loss data saved to: {csv_path}")

    def load(self, path):
        """从文件加载模型"""
        checkpoint = torch.load(path)

        # 重新创建模型（如果需要）
        if not hasattr(self, 'model') or self.model.n_centers != checkpoint.get('n_centers', 20) or \
           self.model.output_dim != checkpoint.get('output_dim', 1) or \
           self.model.input_dim != checkpoint.get('input_dim', 3):
            self.model = TorchRBFModel(
                n_centers=checkpoint.get('n_centers', 20),
                output_dim=checkpoint.get('output_dim', 1),
                sigma=checkpoint.get('sigma', 1.0),
                input_dim=checkpoint.get('input_dim', 3)
            ).to(self.device)

        # 加载模型参数
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

        # 加载标准化参数
        self.x_mean = checkpoint.get('x_mean', 0)
        self.x_std = checkpoint.get('x_std', 1)

        # 加载y的标准化参数，并转换为numpy数组
        y_mean = checkpoint.get('y_mean')
        y_std = checkpoint.get('y_std')

        if y_mean is not None:
            self.y_mean = np.array(y_mean)
        else:
            self.y_mean = None

        if y_std is not None:
            self.y_std = np.array(y_std)
        else:
            self.y_std = None


class ModelWrapper:
    """可序列化的模型包装类"""

    def __init__(self, model, model_type, node_index=None):
        self.model = model
        self.model_type = model_type
        self.node_index = node_index  # 用于MLP模型，指定当前包装器对应的节点索引

    def __call__(self, x):
        """使对象可调用，就像函数一样"""
        if self.model_type == 'rbf':
            # 对于RBF模型，需要将输入拆分为三个独立的角度值
            if isinstance(x, (list, tuple)) and len(x) == 3:
                # 如果输入是长度为3的列表或元组，直接解包
                a1, a2, a3 = x
                return self.model(a1, a2, a3)
            elif isinstance(x, np.ndarray) and x.shape == (3,):
                # 如果输入是形状为(3,)的numpy数组，解包为三个值
                return self.model(x[0], x[1], x[2])
            else:
                raise ValueError("RBF模型需要三个角度值作为输入")
        elif self.model_type in ['mlp', 'torch_rbf']:
            # 对于MLP和TorchRBF模型，我们需要返回特定节点的预测值
            all_predictions = self.model.predict(x)
            # 如果node_index不为None，返回对应节点的预测值
            if self.node_index is not None:
                return all_predictions[self.node_index]
            # 否则返回所有预测值
            return all_predictions
        else:
            # 对于sklearn模型，确保输入是正确的形状
            if isinstance(x, (list, tuple)) and len(x) == 3:
                # 如果输入是三个角度值的元组或列表，直接使用
                return self.model.predict([x])[0]
            elif isinstance(x, np.ndarray) and x.shape == (3,):
                # 如果输入是形状为(3,)的numpy数组，重塑为(1,3)
                return self.model.predict(x.reshape(1, -1))[0]
            else:
                # 兼容旧版本的单个值输入
                return self.model.predict([[x]])[0]


class ProxyModelFactory:
    """代理模型工厂类，用于创建不同类型的代理模型"""

    @staticmethod
    def create_model(model_type, x_train, y_train, **kwargs):
        """
        创建指定类型的代理模型

        参数:
            model_type (str): 模型类型，可选值: 'rbf', 'svr', 'tree', 'forest', 'mlp'
            x_train (numpy.ndarray): 训练输入数据 (角度值)
            y_train (numpy.ndarray): 训练目标数据 (应变值)
            **kwargs: 模型特定的额外参数

        返回:
            ModelWrapper: 可序列化的模型包装对象
        """
        if model_type == 'rbf':
            function = kwargs.get('function', 'cubic')
            # 将x_train从元组列表转换为三个独立的数组
            x_train_array = np.array(x_train)
            a1_values = x_train_array[:, 0]
            a2_values = x_train_array[:, 1]
            a3_values = x_train_array[:, 2]
            # 创建RBF模型，传入三个独立的角度数组和一个应变值数组
            model = Rbf(a1_values, a2_values, a3_values, y_train, function=function)
            return ModelWrapper(model, model_type)

        # linear 和 poly 模型已移除



        elif model_type == 'tree':
            max_depth = kwargs.get('max_depth', None)

            # 确保x_train是numpy数组
            x_train_array = np.array(x_train)
            tree = DecisionTreeRegressor(max_depth=max_depth)
            tree.fit(x_train_array, y_train)
            return ModelWrapper(tree, model_type)



        elif model_type == 'mlp':
            # 获取MLP特定参数
            hidden_dims = kwargs.get('hidden_dims', [64, 128, 64])
            dropout_rate = kwargs.get('dropout_rate', 0.1)
            learning_rate = kwargs.get('learning_rate', 0.0005)
            batch_size = kwargs.get('batch_size', 64)
            epochs = kwargs.get('epochs', 200)
            device = kwargs.get('device', None)

            # 创建一个统一的MLP模型，输出维度等于节点数量
            # 注意：这里我们不再为每个节点创建单独的模型
            # 而是创建一个模型，直接输出所有节点的应变值

            # 这里我们假设y_train是一个单节点的数据
            # 我们需要返回一个特殊的标记，表示这是MLP模型的第一个节点
            # 后续在train_models函数中会特殊处理

            if kwargs.get('is_first_node', False):
                # 如果是第一个节点，创建并返回MLPWrapper
                mlp_wrapper = MLPWrapper(
                    input_dim=3,  # 三个关节角度值
                    hidden_dims=hidden_dims,
                    output_dim=kwargs.get('n_nodes', 1),  # 输出维度等于节点数量
                    dropout_rate=dropout_rate,
                    learning_rate=learning_rate,
                    batch_size=batch_size,
                    epochs=epochs,
                    device=device
                )
                return mlp_wrapper
            else:
                # 如果不是第一个节点，返回None，表示跳过
                return None

        elif model_type == 'torch_rbf':
            # 获取TorchRBF特定参数
            n_centers = kwargs.get('n_centers', 20)
            sigma = kwargs.get('sigma', 1.0)
            learning_rate = kwargs.get('learning_rate', 0.001)
            batch_size = kwargs.get('batch_size', 64)
            epochs = kwargs.get('epochs', 200)
            device = kwargs.get('device', None)

            # 创建一个统一的TorchRBF模型，输出维度等于节点数量
            if kwargs.get('is_first_node', False):
                # 如果是第一个节点，创建并返回TorchRBFWrapper
                torch_rbf_wrapper = TorchRBFWrapper(
                    n_centers=n_centers,
                    output_dim=kwargs.get('n_nodes', 1),  # 输出维度等于节点数量
                    sigma=sigma,
                    input_dim=3,  # 三个关节角度值
                    learning_rate=learning_rate,
                    batch_size=batch_size,
                    epochs=epochs,
                    device=device
                )
                return torch_rbf_wrapper
            else:
                # 如果不是第一个节点，返回None，表示跳过
                return None

        elif model_type == 'transformer_pinn':
            # 获取Transformer-PINN特定参数
            physics_weight = kwargs.get('physics_weight', 0.1)
            nhead = kwargs.get('nhead', 8)
            num_layers = kwargs.get('num_layers', 6)
            
            if kwargs.get('is_first_node', False):
                transformer_pinn_wrapper = TransformerPINNWrapper(
                    input_dim=3,
                    output_dim=kwargs.get('n_nodes', 1),
                    physics_weight=physics_weight,
                    nhead=nhead,
                    num_layers=num_layers
                )
                return transformer_pinn_wrapper
            else:
                return None

        elif model_type == 'pinn_mlp':
            # 获取PINN-MLP特定参数
            hidden_dims = kwargs.get('hidden_dims', [128, 256, 128])
            dropout_rate = kwargs.get('dropout_rate', 0.1)
            learning_rate = kwargs.get('learning_rate', 0.001)
            batch_size = kwargs.get('batch_size', 64)
            epochs = kwargs.get('epochs', 300)
            physics_weight = kwargs.get('physics_weight', 0.1)
            device = kwargs.get('device', None)

            # 创建一个统一的PINN-MLP模型，输出维度等于节点数量
            if kwargs.get('is_first_node', False):
                # 如果是第一个节点，创建并返回PINNMLPWrapper
                pinn_mlp_wrapper = PINNMLPWrapper(
                    input_dim=3,  # a1,a2,a3
                    hidden_dims=hidden_dims,
                    output_dim=kwargs.get('n_nodes', 2606),  # 输出维度等于节点数量
                    dropout_rate=dropout_rate,
                    learning_rate=learning_rate,
                    batch_size=batch_size,
                    epochs=epochs,
                    physics_weight=physics_weight,
                    device=device
                )
                return pinn_mlp_wrapper
            else:
                # 如果不是第一个节点，返回None，表示跳过
                return None

        elif model_type == 'pirbn':
            # 获取PIRBN特定参数
            n_centers = kwargs.get('n_centers', 1000)
            learning_rate = kwargs.get('learning_rate', 0.001)
            batch_size = kwargs.get('batch_size', 64)
            epochs = kwargs.get('epochs', 1000)
            physics_weight = kwargs.get('physics_weight', 0.1)
            device = kwargs.get('device', None)

            # 创建一个统一的PIRBN模型，输出维度等于节点数量
            if kwargs.get('is_first_node', False):
                # 如果是第一个节点，创建并返回PIRBN模型
                # 这里我们返回一个特殊的标记，实际的模型创建在train_models中处理
                return 'pirbn_placeholder'
            else:
                # 如果不是第一个节点，返回None，表示跳过
                return None

        else:
            raise ValueError(f"不支持的模型类型: {model_type}")


def train_models(model_type, x_train, y_train, **kwargs):
    """
    为每个网格节点训练模型

    参数:
        model_type (str): 模型类型
        x_train (numpy.ndarray): 训练角度数据
        y_train (numpy.ndarray): 训练应变数据，形状为 (节点数, 角度数)
        **kwargs: 传递给模型的额外参数

    返回:
        list: 训练好的模型列表，每个网格节点一个模型
    """
    n_nodes = y_train.shape[0]
    model_list = []

    # 对于MLP、TorchRBF、PINN-MLP、PIRBN、GNN、CNN、RNN、Transformer模型，我们使用一个统一的模型来预测所有节点
    if model_type in ['mlp', 'torch_rbf', 'pinn_mlp', 'pirbn', 'gnn', 'cnn', 'rnn', 'transformer']:
        if model_type == 'pirbn':
            # 特殊处理PIRBN模型
            # 需要加载节点坐标
            import pandas as pd

            # 从config中获取坐标文件路径
            from config import MESH_FILE

            try:
                # 读取节点坐标（没有列名的CSV文件）
                coordinates_df = pd.read_csv(MESH_FILE, header=None, encoding='gbk')
                coordinates = coordinates_df.values  # 直接使用所有列作为坐标
                print(f"成功加载 {len(coordinates)} 个节点坐标")
            except Exception as e:
                print(f"加载节点坐标失败: {e}")
                coordinates = None

            # 确保x_train是numpy数组
            x_train_array = np.array(x_train)

            # 使用PIRBN模型创建函数
            pirbn_models = create_pirbn_models(
                x_train_array,
                y_train,
                coordinates,
                **kwargs
            )

            # pirbn_models返回的是一个包装器列表，最后一个元素包含完整模型信息
            model_list = pirbn_models[:-1]  # 前面的是节点包装器
            full_model_info = pirbn_models[-1]  # 最后一个是完整模型信息

            # 保存完整模型信息以便后续使用
            for wrapper in model_list:
                wrapper.full_model_info = full_model_info
        elif model_type in ['gnn', 'cnn', 'rnn', 'transformer']:
            # 对于新的神经网络模型，直接创建包装器
            # 确保x_train是numpy数组
            x_train_array = np.array(x_train)

            # 创建模型参数
            model_kwargs = kwargs.copy()
            model_kwargs['output_dim'] = n_nodes

            # 创建对应的包装器
            if model_type == 'gnn':
                unified_model = GNNWrapper(**model_kwargs)
            elif model_type == 'cnn':
                unified_model = CNNWrapper(**model_kwargs)
            elif model_type == 'rnn':
                unified_model = RNNWrapper(**model_kwargs)
            elif model_type == 'transformer':
                unified_model = TransformerWrapper(**model_kwargs)

            # 训练模型
            unified_model.train(x_train_array, y_train)

            # 为每个节点创建一个ModelWrapper，共享同一个模型
            for i in range(n_nodes):
                model_list.append(ModelWrapper(unified_model, model_type, node_index=i))
        else:
            # 对于其他统一模型（MLP、TorchRBF、PINN-MLP）
            # 创建一个统一的模型
            model_kwargs = kwargs.copy()
            model_kwargs['is_first_node'] = True
            model_kwargs['n_nodes'] = n_nodes

            # 创建模型
            unified_model = ProxyModelFactory.create_model(model_type, x_train, None, **model_kwargs)

            # 训练模型 - 使用所有节点的数据
            # 确保x_train是numpy数组
            x_train_array = np.array(x_train)
            unified_model.train(x_train_array, y_train)

            # 为每个节点创建一个ModelWrapper，共享同一个模型
            for i in range(n_nodes):
                model_list.append(ModelWrapper(unified_model, model_type, node_index=i))
    else:
        # 对于其他模型类型，为每个节点创建一个单独的模型
        # 导入tqdm库用于显示进度条
        from tqdm import tqdm

        # 使用tqdm创建进度条
        progress_bar = tqdm(range(n_nodes), desc=f"训练{model_type}模型", ncols=100)

        for i in progress_bar:
            # 更新进度条描述，显示当前节点
            progress_bar.set_description(f"训练{model_type}模型 - 节点 {i+1}/{n_nodes}")

            model = ProxyModelFactory.create_model(model_type, x_train, y_train[i, :], **kwargs)
            model_list.append(model)

            # 更新进度条，显示完成百分比
            progress_bar.update()

    return model_list


def predict_with_models(model_list, x_test):
    """
    使用训练好的模型列表进行预测

    参数:
        model_list (list): 训练好的模型列表
        x_test (numpy.ndarray): 测试角度数据

    返回:
        tuple: (预测结果数组, 预测时间)
            - 预测结果数组形状为 (节点数, 测试样本数)
            - 预测时间为所有预测的总时间（秒）
    """
    import time
    from scipy.interpolate import Rbf

    n_nodes = len(model_list)
    n_test = len(x_test)
    y_pred = np.zeros((n_nodes, n_test))

    # 开始计时
    start_time = time.time()

    # 检查第一个模型的类型，确定是否可以批量预测
    first_model = model_list[0]

    # 检查是否是新的神经网络模型（GNN、CNN、RNN、Transformer）
    if isinstance(first_model, ModelWrapper) and first_model.model_type in ['gnn', 'cnn', 'rnn', 'transformer']:
        # 获取共享模型
        shared_model = first_model.model

        # 检查所有模型是否共享同一个底层模型
        all_same_model = all(isinstance(model, ModelWrapper) and model.model is shared_model for model in model_list)

        if all_same_model:
            try:
                # 批量预测
                predictions = shared_model.predict(x_test)  # 返回 (n_test, n_nodes)

                # 转置为 (n_nodes, n_test)
                if len(predictions.shape) > 1 and predictions.shape[0] == n_test:
                    predictions = predictions.T

                y_pred = predictions

                # 计算总预测时间
                total_time = time.time() - start_time
                return y_pred, total_time
            except Exception as e:
                print(f"{first_model.model_type}批量预测失败，使用逐个预测: {e}")
                # 如果批量预测失败，回退到逐个预测
                pass

    if isinstance(first_model, ModelWrapper) and first_model.model_type in ['mlp', 'torch_rbf']:
        # 获取共享模型
        shared_model = first_model.model

        # 检查所有模型是否共享同一个底层模型
        all_same_model = True
        for model in model_list[1:]:
            if not isinstance(model, ModelWrapper) or model.model != shared_model:
                all_same_model = False
                break

        # 如果所有模型共享同一个底层模型，使用批量预测
        if all_same_model:
            # 批量预测所有角度
            predictions = shared_model.predict(x_test)  # 形状: (n_test, n_nodes)

            # 转置结果，使其形状为 (n_nodes, n_test)
            if len(predictions.shape) > 1 and predictions.shape[0] == n_test:
                predictions = predictions.T

            # 对于每个节点，从预测结果中提取对应的值
            for i in range(n_nodes):
                model = model_list[i]
                if model.node_index is not None:
                    y_pred[i, :] = predictions[model.node_index, :]
                else:
                    # 如果没有指定node_index，假设模型直接输出单个节点的预测值
                    y_pred[i, :] = predictions

            # 计算总预测时间
            total_time = time.time() - start_time
            return y_pred, total_time

    # 检查是否是PINN-MLP模型
    if isinstance(first_model, PINNMLPWrapper):
        # PINN-MLP模型使用与MLP和TorchRBF相同的处理方式
        # 获取共享模型
        shared_model = first_model

        # 检查所有模型是否共享同一个底层模型
        all_same_model = all(model is first_model for model in model_list)

        if all_same_model:
            # 所有模型都是同一个，可以进行批量预测
            try:
                # 批量预测
                y_pred = shared_model.predict(x_test)  # 返回 (n_test, n_nodes)

                # 转置为 (n_nodes, n_test)
                y_pred = y_pred.T

                # 计算总预测时间
                total_time = time.time() - start_time
                return y_pred, total_time
            except Exception as e:
                print(f"PINN-MLP批量预测失败，使用逐个预测: {e}")
                # 如果批量预测失败，回退到逐个预测
                pass

    # 检查是否是PIRBN模型
    if hasattr(first_model, 'full_model_info') and first_model.full_model_info.get('model_type') == 'pirbn':
        # PIRBN模型批量预测
        try:
            from pirbn_model import predict_with_pirbn

            # 获取完整模型
            full_model = first_model.full_model_info['full_model']

            # 批量预测
            predictions, _ = predict_with_pirbn(full_model, x_test)  # 返回 (n_test, n_nodes)

            # 转置为 (n_nodes, n_test)
            y_pred = predictions.T

            # 计算总预测时间
            total_time = time.time() - start_time
            return y_pred, total_time
        except Exception as e:
            print(f"PIRBN批量预测失败，使用逐个预测: {e}")
            # 如果批量预测失败，回退到逐个预测
            pass

    # 如果不能批量预测，使用逐个预测
    from tqdm import tqdm

    for i in tqdm(range(n_nodes), desc="节点预测进度"):
        model = model_list[i]

        # 尝试批量预测（如果模型支持）
        if isinstance(model, ModelWrapper) and model.model_type in ['mlp', 'torch_rbf']:
            try:
                # 尝试批量预测
                batch_predictions = model(x_test)
                if isinstance(batch_predictions, np.ndarray) and batch_predictions.shape == (n_test,):
                    y_pred[i, :] = batch_predictions
                    continue
            except:
                # 如果批量预测失败，回退到逐个预测
                pass

        # 逐个预测
        for j in tqdm(range(n_test), desc=f"节点{i+1}/{n_nodes}角度预测", leave=False):
            x = x_test[j]

            # 使用模型进行预测
            if isinstance(model, ModelWrapper):
                # 如果是ModelWrapper对象，直接调用
                y_pred[i, j] = model(x)
            elif callable(model):
                # 兼容旧版本的可调用模型
                y_pred[i, j] = model(x)
            elif isinstance(model, Rbf):
                # 兼容旧版本的RBF模型
                # 对于RBF模型，需要将输入拆分为三个独立的角度值
                if isinstance(x, (list, tuple)) and len(x) == 3:
                    a1, a2, a3 = x
                    y_pred[i, j] = model(a1, a2, a3)
                elif isinstance(x, np.ndarray) and x.shape == (3,):
                    y_pred[i, j] = model(x[0], x[1], x[2])
                else:
                    raise ValueError("RBF模型需要三个角度值作为输入")
            elif hasattr(model, 'predict'):
                # 兼容旧版本的sklearn模型
                if isinstance(x, (list, tuple)) and len(x) == 3:
                    # 如果输入是三个角度值的元组或列表，直接使用
                    y_pred[i, j] = model.predict([x])[0]
                elif isinstance(x, np.ndarray) and x.shape == (3,):
                    # 如果输入是形状为(3,)的numpy数组，重塑为(1,3)
                    y_pred[i, j] = model.predict(x.reshape(1, -1))[0]
                else:
                    # 兼容旧版本的单个值输入
                    y_pred[i, j] = model.predict([[x]])[0]
            else:
                raise TypeError(f"不支持的模型类型: {type(model)}")

    # 计算总预测时间
    total_time = time.time() - start_time

    return y_pred, total_time