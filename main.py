"""
机械臂数字孪生系统 - 主程序

此脚本是机械臂数字孪生系统代理模型训练和评估的入口点。
可以通过命令行参数控制执行流程。
"""

import argparse
import os
import time
import numpy as np
import pandas as pd
import joblib
from config import *
import importlib.util

# 检查模块是否可用
def is_module_available(module_name):
    spec = importlib.util.find_spec(module_name)
    return spec is not None

# 确保所需模块已安装
required_modules = ['numpy', 'pandas', 'scipy', 'sklearn', 'matplotlib', 'joblib']
missing_modules = [m for m in required_modules if not is_module_available(m)]
if missing_modules:
    print(f"错误: 缺少以下必要模块: {', '.join(missing_modules)}")
    print("请使用 pip install 安装这些模块后再运行程序。")
    exit(1)


def train_model(args):
    """训练模型"""
    from train import main as train_main
    train_main(args)


def evaluate_model(args):
    """评估模型"""
    from evaluate import main as evaluate_main
    evaluate_main(args)


def compare_models(args):
    """比较不同模型的性能"""
    from evaluate import evaluate_model, load_data
    import matplotlib.pyplot as plt

    # 加载测试数据
    print("正在加载测试数据...")

    # 从CSV文件读取测试角度参数
    import pandas as pd
    test_params_file = os.path.join(args.base_path, '多关节角度数据集\\data\\test_cases_parameters.csv')
    print(f"正在从 {test_params_file} 读取测试角度参数...")

    try:
        # 使用GBK编码读取CSV文件
        test_params_df = pd.read_csv(test_params_file, encoding='gbk')
        # 提取角度参数
        test_angles = [(row['a1'], row['a2'], row['a3']) for _, row in test_params_df.iterrows()]
        print(f"成功读取 {len(test_angles)} 组测试角度参数")
    except Exception as e:
        print(f"读取测试角度参数文件时出错: {e}")
        print("使用默认角度参数...")
        # 使用config中的示例角度
        test_angles = TEST_ANGLES_EXAMPLE

    Y_test, df = load_data(
        base_path=args.base_path,
        angles=test_angles,
        data_folder='多关节角度数据集/data/test',
        column_idx=3
    )

    # 准备比较结果
    models_to_compare = args.models if args.models else AVAILABLE_MODELS
    results = {
        'model_type': [],
        'avg_rmse': [],
        'avg_r2': [],
        'avg_mae': [],
        'avg_time': []
    }

    # 评估每个模型
    for model_type in models_to_compare:
        print(f"\n正在评估 {model_type} 模型...")
        model_path = os.path.join(MODEL_FOLDER, f'训练好的代理模型_a1a2a3_{model_type}.pkl')

        # 检查模型是否存在
        if not os.path.exists(model_path):
            print(f"警告: 模型 {model_path} 不存在，跳过评估")
            continue

        # 加载模型，处理NumPy版本兼容性问题
        try:
            model_list = joblib.load(model_path)
        except (ModuleNotFoundError, ImportError) as e:
            if 'numpy._core' in str(e) or 'numpy.core' in str(e):
                print(f"警告: 模型 {model_path} 存在NumPy版本兼容性问题")
                print("建议重新训练该模型以解决兼容性问题")
                print(f"跳过 {model_type} 模型的评估")
                continue
            else:
                raise e

        # 导入预测函数
        from models import predict_with_models

        # 进行预测
        Y_pred, total_time = predict_with_models(model_list, np.array(test_angles, dtype=np.float32))
        avg_time = total_time / len(test_angles)

        # 评估性能
        metrics = evaluate_model(Y_test, Y_pred)

        # 记录结果
        results['model_type'].append(model_type)
        results['avg_rmse'].append(metrics['avg_rmse'])
        results['avg_r2'].append(metrics['avg_r2'])
        results['avg_mae'].append(metrics['avg_mae'])
        results['avg_time'].append(avg_time)

        # 打印结果
        print(f"  平均 RMSE: {metrics['avg_rmse']:.4f}")
        print(f"  平均 R²: {metrics['avg_r2']:.4f}")
        print(f"  平均 MAE: {metrics['avg_mae']:.4f}")
        print(f"  总预测时间: {total_time:.3f}秒，平均每个角度: {avg_time:.3f}秒")

    # 将比较结果写入TXT文件
    if results['model_type']:
        # 创建结果文件路径
        result_file = os.path.join(RESULT_FOLDER, 'model_comparison_results.txt')

        # 写入结果
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(f"\n{'-'*50}\n")
            f.write(f"模型性能比较结果\n")
            f.write(f"{'-'*50}\n\n")

            # 写入表头
            f.write(r"模型类型".ljust(15) + r"RMSE".ljust(15) + r"R²".ljust(15) + r"MAE".ljust(15) + r"预测时间(秒)".ljust(15) + "\n")
            f.write(f"{'-'*75}\n")

            # 写入每个模型的结果
            for i, model_type in enumerate(results['model_type']):
                f.write(f"{model_type:<15}{results['avg_rmse'][i]:<15.6f}{results['avg_r2'][i]:<15.6f}{results['avg_mae'][i]:<15.6f}{results['avg_time'][i]:<15.6f}\n")

            # 写入最佳模型
            best_rmse_idx = np.argmin(results['avg_rmse'])
            best_r2_idx = np.argmax(results['avg_r2'])
            best_mae_idx = np.argmin(results['avg_mae'])
            best_time_idx = np.argmin(results['avg_time'])

            f.write(f"\n\n== 最佳模型 ==\n")
            f.write(f"最低 RMSE: {results['model_type'][best_rmse_idx]} ({results['avg_rmse'][best_rmse_idx]:.6f})\n")
            f.write(f"最高 R²: {results['model_type'][best_r2_idx]} ({results['avg_r2'][best_r2_idx]:.6f})\n")
            f.write(f"最低 MAE: {results['model_type'][best_mae_idx]} ({results['avg_mae'][best_mae_idx]:.6f})\n")
            f.write(f"最快预测时间: {results['model_type'][best_time_idx]} ({results['avg_time'][best_time_idx]:.6f} 秒)\n")

        print(f"\n模型比较结果已保存到: {result_file}")

        # 绘制比较图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # RMSE比较
        axes[0, 0].bar(results['model_type'], results['avg_rmse'], color='blue', alpha=0.7)
        axes[0, 0].set_title('平均RMSE (越低越好)')
        axes[0, 0].set_ylabel('RMSE')
        for i, v in enumerate(results['avg_rmse']):
            axes[0, 0].text(i, v + 0.001, f'{v:.4f}', ha='center')

        # R²比较
        axes[0, 1].bar(results['model_type'], results['avg_r2'], color='green', alpha=0.7)
        axes[0, 1].set_title('平均R² (越高越好)')
        axes[0, 1].set_ylabel('R²')
        for i, v in enumerate(results['avg_r2']):
            axes[0, 1].text(i, v - 0.03, f'{v:.4f}', ha='center')

        # MAE比较
        axes[1, 0].bar(results['model_type'], results['avg_mae'], color='purple', alpha=0.7)
        axes[1, 0].set_title('平均MAE (越低越好)')
        axes[1, 0].set_ylabel('MAE')
        for i, v in enumerate(results['avg_mae']):
            axes[1, 0].text(i, v + 0.001, f'{v:.4f}', ha='center')

        # 预测时间比较
        axes[1, 1].bar(results['model_type'], results['avg_time'], color='orange', alpha=0.7)
        axes[1, 1].set_title('平均预测时间 (越低越好)')
        axes[1, 1].set_ylabel('时间 (秒)')
        for i, v in enumerate(results['avg_time']):
            axes[1, 1].text(i, v + 0.001, f'{v:.3f}s', ha='center')

        plt.suptitle('不同模型性能比较')
        plt.tight_layout()

        # 保存比较结果
        save_path = os.path.join(RESULT_FOLDER, 'model_comparison.png')
        plt.savefig(save_path)
        plt.close()

        print(f"模型比较图表已保存到: {save_path}")
    else:
        print("\n没有可用的模型进行比较")


def interactive_menu():
    """交互式菜单，当没有命令行参数时使用"""
    import sys
    from types import SimpleNamespace

    print("\n" + "="*50)
    print("机械臂数字孪生系统 - 代理模型交互式菜单")
    print("="*50)

    while True:
        print("\n请选择要执行的操作:")
        print("1. 训练模型")
        print("2. 评估模型")
        print("3. 比较模型")
        print("0. 退出程序")


        choice = input("\n请输入选项编号: ")

        if choice == '0':
            print("程序已退出")
            sys.exit(0)

        elif choice == '1':
            # 训练模型
            print("\n" + "-"*50)
            print("训练模型")
            print("-"*50)

            # 选择模型类型
            print("\n可用的模型类型:")
            for i, model_type in enumerate(AVAILABLE_MODELS, 1):
                print(f"{i}. {model_type}")

            model_idx = int(input("\n请选择模型类型 (输入编号): ")) - 1
            if model_idx < 0 or model_idx >= len(AVAILABLE_MODELS):
                print("无效的选择，使用默认模型类型")
                model_type = DEFAULT_MODEL_TYPE
            else:
                model_type = AVAILABLE_MODELS[model_idx]

            # 创建参数对象
            args = SimpleNamespace(
                base_path=BASE_PATH,
                model_type=model_type
            )

            # 添加模型特定参数
            if model_type in MODEL_PARAMS:
                for param_name, param_value in MODEL_PARAMS[model_type].items():
                    setattr(args, param_name, param_value)

            # 执行训练
            print(f"\n开始训练 {model_type} 模型...")
            train_model(args)

        elif choice == '2':
            # 评估模型
            print("\n" + "-"*50)
            print("增强评估模型")
            print("-"*50)

            # 检查可用的模型
            model_folder = os.path.join(BASE_PATH, '模型')
            available_trained_models = []

            if os.path.exists(model_folder):
                for file in os.listdir(model_folder):
                    if file.startswith('训练好的代理模型_a1a2a3_') and file.endswith('.pkl'):
                        model_name = file.replace('训练好的代理模型_a1a2a3_', '').replace('.pkl', '')
                        if model_name in AVAILABLE_MODELS:
                            available_trained_models.append(model_name)

            if not available_trained_models:
                print("❌ 没有找到已训练的模型文件")
                print("请先训练模型后再进行评估")
                continue

            # 显示可用的已训练模型
            print("\n✅ 可用的已训练模型:")
            for i, model_type in enumerate(available_trained_models, 1):
                print(f"{i}. {model_type}")

            try:
                model_idx = int(input("\n请选择模型类型 (输入编号): ")) - 1
                if model_idx < 0 or model_idx >= len(available_trained_models):
                    print("无效的选择，使用第一个可用模型")
                    model_type = available_trained_models[0]
                else:
                    model_type = available_trained_models[model_idx]
            except ValueError:
                print("输入无效，使用第一个可用模型")
                model_type = available_trained_models[0]

            print(f"\n选择的模型: {model_type}")

            # 评估选项
            print("\n📊 评估选项:")
            print("1. 增强评估 (基础指标 + 应变分析 + 高级图表)") 
            print("2. 完整评估 (增强评估 + 云图)")             

            try:
                eval_option = input("\n请选择评估类型 (1-2): ")  
            except:
                eval_option = "1"  # 默认选择改为1

            # 根据选择设置参数
            if eval_option == "2":
                plot_clouds = True
                print("\n🎯 将进行完整评估 (包含云图)...")
                print("⚠️  注意: 云图生成可能需要较长时间")
            else:  # 所有其他情况默认选择增强评估
                plot_clouds = False
                print("\n✨ 将进行增强评估...")

            # 创建参数对象
            args = SimpleNamespace(
                base_path=BASE_PATH,
                model_type=model_type,
                plot_clouds=plot_clouds
            )

            # 显示将要生成的内容
            print(f"\n📋 评估内容预览:")
            print(f"  📈 评估指标分析 (RMSE, R², MAE)")
            print(f"  📊 应变统计分析 (平均值、最大值、趋势)")
            print(f"  🎨 高级可视化图表 (分布图、箱线图、散点图)")
            print(f"  💾 CSV数据导出 (应变统计数据)")
            if plot_clouds:
                print(f"  🌈 3D云图可视化 (每个工况)")
            print(f"  🏷️  所有图表使用英文标题和图例")

            # 确认执行
            confirm = input(f"\n确认开始评估 {model_type} 模型? (y/n): ").lower()
            if confirm != 'y':
                print("评估已取消")
                continue

            # 执行评估
            print(f"\n🚀 开始增强评估 {model_type} 模型...")
            print("=" * 60)

            try:
                evaluate_model(args)

                # 显示结果位置
                result_folder = os.path.join(BASE_PATH, '云图结果', model_type)
                print("\n" + "=" * 60)
                print("🎉 增强评估完成!")
                print("=" * 60)
                print(f"📁 结果保存位置: {result_folder}")
                print("\n📊 生成的文件包括:")
                print("  ✅ 应变统计CSV文件")
                print("  ✅ 应变对比图表")
                print("  ✅ 高级评估指标图")
                print("  ✅ 传统评估指标分布图")
                print("  ✅ 详细评估结果文本")
                if plot_clouds:
                    print("  ✅ 各工况3D云图")

            except Exception as e:
                print(f"\n❌ 评估过程中出现错误: {e}")
                print("请检查模型文件和数据文件是否完整")

        elif choice == '3':
            # 比较模型
            print("\n" + "-"*50)
            print("比较模型")
            print("-"*50)

            # 选择要比较的模型
            print("\n可用的模型类型:")
            for i, model_type in enumerate(AVAILABLE_MODELS, 1):
                print(f"{i}. {model_type}")

            print("\n请选择要比较的模型 (多选，用空格分隔编号，直接回车比较所有模型):")
            models_input = input()

            if models_input.strip():
                try:
                    model_indices = [int(idx) - 1 for idx in models_input.split()]
                    models = [AVAILABLE_MODELS[idx] for idx in model_indices if 0 <= idx < len(AVAILABLE_MODELS)]
                    if not models:
                        print("无效的选择，将比较所有模型")
                        models = None
                except:
                    print("输入格式错误，将比较所有模型")
                    models = None
            else:
                models = None

            # 创建参数对象
            args = SimpleNamespace(
                base_path=BASE_PATH,
                models=models
            )

            # 执行比较
            print("\n开始比较模型...")
            compare_models(args)

        else:
            print("无效的选择，请重新输入")


def main():
    """主函数"""
    import sys

    # 检查是否有命令行参数
    if len(sys.argv) == 1:
        # 没有参数，启动交互式菜单
        interactive_menu()
        return

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='机械臂数字孪生系统代理模型训练和评估')

    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='要执行的命令')

    # 训练命令
    train_parser = subparsers.add_parser('train', help='训练代理模型')
    train_parser.add_argument('--base_path', type=str, default=BASE_PATH,
                             help='项目基础路径')
    train_parser.add_argument('--model_type', type=str, default=DEFAULT_MODEL_TYPE,
                             choices=AVAILABLE_MODELS, help='模型类型')
    # 添加模型特定参数（避免重复参数）
    added_params = set()
    for model, params in MODEL_PARAMS.items():
        for param_name, param_value in params.items():
            if param_name not in added_params:
                train_parser.add_argument(f'--{param_name}', type=type(param_value) if param_value is not None else int,
                                         default=param_value, help=f'模型的{param_name}参数')
                added_params.add(param_name)

    # 评估命令
    eval_parser = subparsers.add_parser('evaluate', help='评估代理模型')
    eval_parser.add_argument('--base_path', type=str, default=BASE_PATH,
                            help='项目基础路径')
    eval_parser.add_argument('--model_type', type=str, default=DEFAULT_MODEL_TYPE,
                            choices=AVAILABLE_MODELS, help='模型类型')
    eval_parser.add_argument('--plot_clouds', action='store_true',
                            help='是否生成云图')

    # 比较命令
    compare_parser = subparsers.add_parser('compare', help='比较不同模型的性能')
    compare_parser.add_argument('--base_path', type=str, default=BASE_PATH,
                               help='项目基础路径')
    compare_parser.add_argument('--models', nargs='+', choices=AVAILABLE_MODELS,
                               help='要比较的模型列表，不指定则比较所有可用模型')

    # 解析参数
    args = parser.parse_args()

    # 执行命令
    if args.command == 'train':
        train_model(args)
    elif args.command == 'evaluate':
        evaluate_model(args)
    elif args.command == 'compare':
        compare_models(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    start_time = time.time()
    main()
    print(f"\n总运行时间: {time.time() - start_time:.2f}秒")