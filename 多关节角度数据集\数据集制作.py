import csv
import pandas as pd
import time
import numpy as np
from sklearn.neighbors import KNeighborsClassifier  # KNN
from scipy.interpolate import Rbf
from tqdm import tqdm
import os
import ast # To safely evaluate string representation of tuples

# --- 设置文件路径 ---
BASE_PROJECT_DIR = 'D:\\Desktop\\机械臂数字孪生\\多关节角度数据集'

# 定义保存处理后数据的路径
TRAIN_SAVE_PATH = os.path.join(BASE_PROJECT_DIR, 'data', 'train')
TEST_SAVE_PATH = os.path.join(BASE_PROJECT_DIR, 'data', 'test')

# 定义原始网格数据路径（保持不变）
MESH_DATA_PATH = 'D:\\Desktop\\机械臂数字孪生\\模型\\细化r2-Jibot.csv'

# 定义 fea 文件夹及其子文件夹的路径
FEA_DIR = os.path.join(BASE_PROJECT_DIR, 'fea')
TRAIN_FEA_DIR = os.path.join(FEA_DIR, 'train')
TEST_FEA_DIR = os.path.join(FEA_DIR, 'test')
CASES_FILE_PATH = os.path.join(FEA_DIR, 'cases.csv') # cases.csv 文件路径

# --- 确保输出路径存在 ---
os.makedirs(TRAIN_SAVE_PATH, exist_ok=True)
os.makedirs(TEST_SAVE_PATH, exist_ok=True)
print(f"已确保处理后数据输出路径存在: {TRAIN_SAVE_PATH} 和 {TEST_SAVE_PATH}")

# --- 删除冗余节点并保存节点编号（保持不变） ---
print("开始处理网格数据，删除冗余节点并保存节点编号...")
data_mesh_raw = pd.read_csv(MESH_DATA_PATH, header=None, dtype='float32')
data_mesh_raw.columns = ['x', 'y', 'z']
mesh_unique = data_mesh_raw.drop_duplicates() # 删除冗余节点

# 保存无重复节点的网格
UNIQUE_NODES_CSV = '无重复节点_R2_a1a2a3.csv'
mesh_unique.to_csv(UNIQUE_NODES_CSV, index=False, header=None)
print(f"无重复节点网格已保存到 {UNIQUE_NODES_CSV}")

# 保存节点编号
temp_indices = list(range(0, len(mesh_unique)))
mesh_unique['zuobiao'] = temp_indices
# 为了合并，data_mesh_raw 需要一个临时索引
data_mesh_raw_with_idx = data_mesh_raw.reset_index()
mesh_unique_with_idx = mesh_unique.reset_index()

# 使用 merge 获取原始网格中每个点的“无重复节点”索引
# 这里使用 'left' merge，以保留原始数据中的所有行，并匹配到去重后的节点索引
data_merged = pd.merge(data_mesh_raw_with_idx, mesh_unique_with_idx[['x', 'y', 'z', 'zuobiao']],
                       on=['x', 'y', 'z'], how='left')

# 确保所有节点都找到了对应的索引，zuobiao 列是最终的节点索引
zuobiao = data_merged['zuobiao']
NODE_INDEX_CSV = '节点索引_R2_a1a2a3.csv'
zuobiao.to_csv(NODE_INDEX_CSV, index=False, header=None)
print(f"节点索引已保存到 {NODE_INDEX_CSV}")
print("无重复节点_R2_a1a2a3.csv 和 节点索引_R2_a1a2a3.csv 可用于 Unity 中重新生成网格，保证对齐一致。")

# 读取无重复节点的网格数据，这将用于后续的插值计算
mesh_for_interpolation = pd.read_csv(UNIQUE_NODES_CSV, header=None, dtype='float64')
mesh_for_interpolation.columns = ['x', 'y', 'z']
print(f"已加载用于插值的无重复节点网格数据，形状: {mesh_for_interpolation.shape}")

# --- 定义生成数据的函数 ---
def generate_data(cases_df_subset, fea_input_dir, save_path):
    """
    根据给定的工况子集，读取对应的fea文件，进行插值计算并保存结果。
    
    Args:
        cases_df_subset (pd.DataFrame): 包含 '文件名' 和 '角度值' 的DataFrame子集。
        fea_input_dir (str): 存储 fea 工况文件的输入目录 (如 fea/train 或 fea/test)。
        save_path (str): 处理后数据保存的目录 (如 data/train 或 data/test)。
    """
    for index, row in cases_df_subset.iterrows():
        filename = row['文件名']
        angles = row['角度值'] # 这是一个元组 (a1, a2, a3)
        
        # 构造fea文件的完整路径
        fea_file_path = os.path.join(fea_input_dir, filename)

        if not os.path.exists(fea_file_path):
            print(f"警告: 找不到fea文件 '{fea_file_path}'，跳过此工况。")
            continue

        print(f"\n开始处理工况: {filename}")
        
        try:
            # 读取有限元分析结果文件
            # '节点数' 设为索引列，但注意您的 fea 文件列名是 'X位置 (m)', 'Y位置 (m)', 'Z位置 (m)', '第一主应变 (mm)'
            # 如果您的 fea 文件没有 '节点数' 这一列作为索引，可能需要调整 `index_col` 或直接删除它
            fea = pd.read_csv(fea_file_path, encoding='utf-8')
            
            # 确保列名与预期一致，并去除重复的XYZ位置节点
            fea.columns = ['X位置 (m)', 'Y位置 (m)', 'Z位置 (m)', '第一主应变 (mm)']
            fea = fea.drop_duplicates(subset=['X位置 (m)', 'Y位置 (m)', 'Z位置 (m)'])
            fea.columns = ['x', 'y', 'z', 'mm'] # 重命名为方便后续处理的列名
            
            # 使用 KNN 进行插值计算
            knn = KNeighborsClassifier(n_neighbors=1, algorithm='ball_tree')
            y_dummy = [1] * len(fea) # KNN分类器的 dummy 标签
            knn.fit(fea.loc[:, 'x':'z'], y_dummy)
            
            # 寻找每个目标网格点最近的30个 fea_cal 点
            distance, points = knn.kneighbors(mesh_for_interpolation.iloc[:, 0:3], n_neighbors=30, return_distance=True)
            
            # 初始化用于Rbf的数据矩阵
            fea_cal = np.zeros((30, 4)) # 存储 x, y, z, mm

            start_interp = time.perf_counter()
            # 针对 mesh_for_interpolation 中的每个点进行 Rbf 插值
            interpolated_values = []
            for i in tqdm(range(len(mesh_for_interpolation)), desc=f"插值 {filename}"):
                # 获取当前目标点周围的30个最近的 fea 点
                for j in range(30):
                    fea_cal[j, :] = fea.iloc[points[i, j], :].values # 确保获取的是 numpy 数组值
                
                # 创建Rbf插值函数
                # 注意: Rbf 对输入的点数量有要求，如果 k_neighbors 不当或fea_cal中有重复点可能报错
                try:
                    func = Rbf(fea_cal[:, 0], fea_cal[:, 1], fea_cal[:, 2], fea_cal[:, 3], function='gaussian')
                    # 计算当前目标点的插值结果
                    interpolated_value = func(mesh_for_interpolation.iloc[i, 0], 
                                              mesh_for_interpolation.iloc[i, 1], 
                                              mesh_for_interpolation.iloc[i, 2])
                    interpolated_values.append(interpolated_value)
                except ValueError as ve:
                    print(f"Rbf 插值错误（ValueError）在工况 {filename}, 节点 {i}: {ve}")
                    # 如果Rbf失败，可以考虑使用平均值或其他备用策略
                    # 这里简化处理，如果插值失败则填入NaN
                    interpolated_values.append(np.nan) 
                except Exception as ex:
                    print(f"Rbf 插值发生未知错误在工况 {filename}, 节点 {i}: {ex}")
                    interpolated_values.append(np.nan)


            end_interp = time.perf_counter()
            print(f'工况 {filename} 计算时长：{end_interp - start_interp:.2f} s')

            # 将插值结果添加到 mesh_for_interpolation DataFrame
            # 创建一个副本以避免 SettingWithCopyWarning
            current_mesh_data = mesh_for_interpolation.copy() 
            current_mesh_data['strain'] = interpolated_values

            # 保存插值结果
            
            output_file_name = f"data_a1_{angles[0]}_a2_{angles[1]}_a3_{angles[2]}.csv"
            output_file_path = os.path.join(save_path, output_file_name)
            current_mesh_data.to_csv(output_file_path, index=False)
            print(f'数据已保存到 {output_file_path}')

        except Exception as e:
            print(f"处理工况 '{filename}' 时发生错误: {e}")
            import traceback
            traceback.print_exc()

# --- 主程序逻辑 ---
def main():
    start_total_time = time.time()
    
    # 读取 cases.csv 文件来获取所有工况及其角度
    if not os.path.exists(CASES_FILE_PATH):
        print(f"错误: 未找到 'cases.csv' 文件，请确保其路径正确: {CASES_FILE_PATH}")
        return

    try:
        all_cases_df = pd.read_csv(CASES_FILE_PATH)
        # 将 '角度值' 列的字符串表示转换为元组
        all_cases_df['角度值'] = all_cases_df['角度值'].apply(ast.literal_eval)
        print(f"已从 '{CASES_FILE_PATH}' 读取 {len(all_cases_df)} 条工况数据。")
    except Exception as e:
        print(f"读取或解析 'cases.csv' 文件时发生错误: {e}")
        return

    # 根据 '文件名' 确定是训练集还是测试集
    # 这里假设文件名能对应到 train_fea_dir 和 test_fea_dir 里面的文件
    # 划分 logic: 直接根据文件所在的目录来区分
    
    # 收集训练集和测试集的工况子集 DataFrame
    train_cases_list = []
    test_cases_list = []

    # 遍历 all_cases_df，检查文件存在于哪个 fea 目录下
    for index, row in all_cases_df.iterrows():
        filename = row['文件名']
        # 检查是否在训练 fea 目录
        if os.path.exists(os.path.join(TRAIN_FEA_DIR, filename)):
            train_cases_list.append(row)
        # 检查是否在测试 fea 目录
        elif os.path.exists(os.path.join(TEST_FEA_DIR, filename)):
            test_cases_list.append(row)
        else:
            print(f"警告: 工况文件 '{filename}' 既不在 '{TRAIN_FEA_DIR}' 也不在 '{TEST_FEA_DIR}'，跳过。")

    train_cases_df = pd.DataFrame(train_cases_list)
    test_cases_df = pd.DataFrame(test_cases_list)

    if train_cases_df.empty and test_cases_df.empty:
        print("未找到任何在 'train' 或 'test' fea 目录下的工况文件。请确保您的文件已正确划分。")
        return

    print(f"\n检测到 {len(train_cases_df)} 个训练工况和 {len(test_cases_df)} 个测试工况。")

    # 生成训练数据
    if not train_cases_df.empty:
        print("\n--- 开始生成训练数据 ---")
        generate_data(train_cases_df, TRAIN_FEA_DIR, TRAIN_SAVE_PATH)
    else:
        print("\n没有训练工况，跳过训练数据生成。")

    # 生成测试数据
    if not test_cases_df.empty:
        print("\n--- 开始生成测试数据 ---")
        generate_data(test_cases_df, TEST_FEA_DIR, TEST_SAVE_PATH)
    else:
        print("\n没有测试工况，跳过测试数据生成。")
    
    end_total_time = time.time()
    print(f"\n所有数据处理完成！总时长: {end_total_time - start_total_time:.2f} 秒")

if __name__ == "__main__":
    main()